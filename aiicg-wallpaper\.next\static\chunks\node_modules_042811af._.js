(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "hasA11yProp": (()=>hasA11yProp),
    "mergeClasses": (()=>mergeClasses),
    "toCamelCase": (()=>toCamelCase),
    "toKebabCase": (()=>toKebabCase),
    "toPascalCase": (()=>toPascalCase)
});
const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const toCamelCase = (string)=>string.replace(/^([A-Z])|[\s-_]+(\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());
const toPascalCase = (string)=>{
    const camelCase = toCamelCase(string);
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);
};
const mergeClasses = (...classes)=>classes.filter((className, index, array)=>{
        return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
    }).join(" ").trim();
const hasA11yProp = (props)=>{
    for(const prop in props){
        if (prop.startsWith("aria-") || prop === "role" || prop === "title") {
            return true;
        }
    }
};
;
 //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>defaultAttributes)
});
var defaultAttributes = {
    xmlns: "http://www.w3.org/2000/svg",
    width: 24,
    height: 24,
    viewBox: "0 0 24 24",
    fill: "none",
    stroke: "currentColor",
    strokeWidth: 2,
    strokeLinecap: "round",
    strokeLinejoin: "round"
};
;
 //# sourceMappingURL=defaultAttributes.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>Icon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
;
;
;
const Icon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ color = "currentColor", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = "", children, iconNode, ...rest }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        ref,
        ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$defaultAttributes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        width: size,
        height: size,
        stroke: color,
        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])("lucide", className),
        ...!children && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasA11yProp"])(rest) && {
            "aria-hidden": "true"
        },
        ...rest
    }, [
        ...iconNode.map(([tag, attrs])=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(tag, attrs)),
        ...Array.isArray(children) ? children : [
            children
        ]
    ]));
;
 //# sourceMappingURL=Icon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "default": (()=>createLucideIcon)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/Icon.js [app-client] (ecmascript)");
;
;
;
const createLucideIcon = (iconName, iconNode)=>{
    const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$Icon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            ref,
            iconNode,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeClasses"])(`lucide-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toKebabCase"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName))}`, `lucide-${iconName}`, className),
            ...props
        }));
    Component.displayName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$shared$2f$src$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toPascalCase"])(iconName);
    return Component;
};
;
 //# sourceMappingURL=createLucideIcon.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>WandSparkles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",
            key: "ul74o6"
        }
    ],
    [
        "path",
        {
            d: "m14 7 3 3",
            key: "1r5n42"
        }
    ],
    [
        "path",
        {
            d: "M5 6v4",
            key: "ilb8ba"
        }
    ],
    [
        "path",
        {
            d: "M19 14v4",
            key: "blhpug"
        }
    ],
    [
        "path",
        {
            d: "M10 2v2",
            key: "7u0qdc"
        }
    ],
    [
        "path",
        {
            d: "M7 8H3",
            key: "zfb6yr"
        }
    ],
    [
        "path",
        {
            d: "M21 16h-4",
            key: "1cnmox"
        }
    ],
    [
        "path",
        {
            d: "M11 3H9",
            key: "1obp7u"
        }
    ]
];
const WandSparkles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("wand-sparkles", __iconNode);
;
 //# sourceMappingURL=wand-sparkles.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js [app-client] (ecmascript) <export default as Wand2>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Wand2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2d$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wand$2d$sparkles$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wand-sparkles.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>LoaderCircle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M21 12a9 9 0 1 1-6.219-8.56",
            key: "13zald"
        }
    ]
];
const LoaderCircle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("loader-circle", __iconNode);
;
 //# sourceMappingURL=loader-circle.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Loader2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Monitor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "rect",
        {
            width: "20",
            height: "14",
            x: "2",
            y: "3",
            rx: "2",
            key: "48i651"
        }
    ],
    [
        "line",
        {
            x1: "8",
            x2: "16",
            y1: "21",
            y2: "21",
            key: "1svkeh"
        }
    ],
    [
        "line",
        {
            x1: "12",
            x2: "12",
            y1: "17",
            y2: "21",
            key: "vw1qmm"
        }
    ]
];
const Monitor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("monitor", __iconNode);
;
 //# sourceMappingURL=monitor.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript) <export default as Monitor>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Monitor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$monitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$monitor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/monitor.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/middleware.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.TARGET_URL_HEADER = void 0;
exports.withMiddleware = withMiddleware;
exports.withProxy = withProxy;
/**
 * Setup a execution chain of middleware functions.
 *
 * @param middlewares one or more middleware functions.
 * @returns a middleware function that executes the given middlewares in order.
 */ function withMiddleware(...middlewares) {
    return (config)=>middlewares.reduce((configPromise, middleware)=>configPromise.then((req)=>middleware(req)), Promise.resolve(config));
}
exports.TARGET_URL_HEADER = "x-fal-target-url";
function withProxy(config) {
    // when running on the server, we don't need to proxy the request
    if (typeof window === "undefined") {
        return (requestConfig)=>Promise.resolve(requestConfig);
    }
    return (requestConfig)=>Promise.resolve(Object.assign(Object.assign({}, requestConfig), {
            url: config.targetUrl,
            headers: Object.assign(Object.assign({}, requestConfig.headers || {}), {
                [exports.TARGET_URL_HEADER]: requestConfig.url
            })
        }));
} //# sourceMappingURL=middleware.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/response.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ValidationError = exports.ApiError = void 0;
exports.defaultResponseHandler = defaultResponseHandler;
class ApiError extends Error {
    constructor({ message, status, body }){
        super(message);
        this.name = "ApiError";
        this.status = status;
        this.body = body;
    }
}
exports.ApiError = ApiError;
class ValidationError extends ApiError {
    constructor(args){
        super(args);
        this.name = "ValidationError";
    }
    get fieldErrors() {
        // NOTE: this is a hack to support both FastAPI/Pydantic errors
        // and some custom 422 errors that might not be in the Pydantic format.
        if (typeof this.body.detail === "string") {
            return [
                {
                    loc: [
                        "body"
                    ],
                    msg: this.body.detail,
                    type: "value_error"
                }
            ];
        }
        return this.body.detail || [];
    }
    getFieldErrors(field) {
        return this.fieldErrors.filter((error)=>error.loc[error.loc.length - 1] === field);
    }
}
exports.ValidationError = ValidationError;
function defaultResponseHandler(response) {
    return __awaiter(this, void 0, void 0, function*() {
        var _a;
        const { status, statusText } = response;
        const contentType = (_a = response.headers.get("Content-Type")) !== null && _a !== void 0 ? _a : "";
        if (!response.ok) {
            if (contentType.includes("application/json")) {
                const body = yield response.json();
                const ErrorType = status === 422 ? ValidationError : ApiError;
                throw new ErrorType({
                    message: body.message || statusText,
                    status,
                    body
                });
            }
            throw new ApiError({
                message: `HTTP ${status}: ${statusText}`,
                status
            });
        }
        if (contentType.includes("application/json")) {
            return response.json();
        }
        if (contentType.includes("text/html")) {
            return response.text();
        }
        if (contentType.includes("application/octet-stream")) {
            return response.arrayBuffer();
        }
        // TODO convert to either number or bool automatically
        return response.text();
    });
} //# sourceMappingURL=response.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/config.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.credentialsFromEnv = void 0;
exports.resolveDefaultFetch = resolveDefaultFetch;
exports.config = config;
exports.getConfig = getConfig;
exports.getRestApiUrl = getRestApiUrl;
const middleware_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/middleware.js [app-client] (ecmascript)");
const response_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/response.js [app-client] (ecmascript)");
function resolveDefaultFetch() {
    if (typeof fetch === "undefined") {
        throw new Error("Your environment does not support fetch. Please provide your own fetch implementation.");
    }
    return fetch;
}
/**
 * Checks if the required FAL environment variables are set.
 *
 * @returns `true` if the required environment variables are set,
 * `false` otherwise.
 */ function hasEnvVariables() {
    return typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] !== "undefined" && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env && (typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY !== "undefined" || typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY_ID !== "undefined" && typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY_SECRET !== "undefined");
}
const credentialsFromEnv = ()=>{
    if (!hasEnvVariables()) {
        return undefined;
    }
    if (typeof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY !== "undefined") {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY;
    }
    return `${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY_ID}:${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.FAL_KEY_SECRET}`;
};
exports.credentialsFromEnv = credentialsFromEnv;
const DEFAULT_CONFIG = {
    credentials: exports.credentialsFromEnv,
    suppressLocalCredentialsWarning: false,
    requestMiddleware: (request)=>Promise.resolve(request),
    responseHandler: response_1.defaultResponseHandler
};
let configuration;
/**
 * Configures the fal serverless client.
 *
 * @param config the new configuration.
 */ function config(config) {
    var _a;
    configuration = Object.assign(Object.assign(Object.assign({}, DEFAULT_CONFIG), config), {
        fetch: (_a = config.fetch) !== null && _a !== void 0 ? _a : resolveDefaultFetch()
    });
    if (config.proxyUrl) {
        configuration = Object.assign(Object.assign({}, configuration), {
            requestMiddleware: (0, middleware_1.withMiddleware)((0, middleware_1.withProxy)({
                targetUrl: config.proxyUrl
            }), configuration.requestMiddleware)
        });
    }
    const { credentials: resolveCredentials, suppressLocalCredentialsWarning } = configuration;
    const credentials = typeof resolveCredentials === "function" ? resolveCredentials() : resolveCredentials;
    if (typeof window !== "undefined" && credentials && !suppressLocalCredentialsWarning) {
        console.warn("The fal credentials are exposed in the browser's environment. " + "That's not recommended for production use cases.");
    }
}
/**
 * Get the current fal serverless client configuration.
 *
 * @returns the current client configuration.
 */ function getConfig() {
    if (!configuration) {
        console.info("Using default configuration for the fal client");
        return Object.assign(Object.assign({}, DEFAULT_CONFIG), {
            fetch: resolveDefaultFetch()
        });
    }
    return configuration;
}
/**
 * @returns the URL of the fal serverless rest api endpoint.
 */ function getRestApiUrl() {
    return "https://rest.alpha.fal.ai";
} //# sourceMappingURL=config.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/package.json (json)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v(JSON.parse("{\"name\":\"@fal-ai/serverless-client\",\"description\":\"Deprecation note: this library has been deprecated in favor of @fal-ai/client\",\"version\":\"0.15.0\",\"license\":\"MIT\",\"repository\":{\"type\":\"git\",\"url\":\"https://github.com/fal-ai/fal-js.git\",\"directory\":\"libs/client\"},\"keywords\":[\"fal\",\"serverless\",\"client\",\"ai\",\"ml\"],\"dependencies\":{\"@msgpack/msgpack\":\"^3.0.0-beta2\",\"eventsource-parser\":\"^1.1.2\",\"robot3\":\"^0.4.1\"},\"engines\":{\"node\":\">=18.0.0\"},\"main\":\"./src/index.js\",\"type\":\"commonjs\"}"));}}),
"[project]/node_modules/@fal-ai/serverless-client/src/runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/* eslint-disable @typescript-eslint/no-var-requires */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.isBrowser = isBrowser;
exports.getUserAgent = getUserAgent;
function isBrowser() {
    return typeof window !== "undefined" && typeof window.document !== "undefined";
}
let memoizedUserAgent = null;
function getUserAgent() {
    if (memoizedUserAgent !== null) {
        return memoizedUserAgent;
    }
    const packageInfo = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/package.json (json)");
    memoizedUserAgent = `${packageInfo.name}/${packageInfo.version}`;
    return memoizedUserAgent;
} //# sourceMappingURL=runtime.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/request.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dispatchRequest = dispatchRequest;
const config_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/config.js [app-client] (ecmascript)");
const runtime_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/runtime.js [app-client] (ecmascript)");
const isCloudflareWorkers = typeof navigator !== "undefined" && (navigator === null || navigator === void 0 ? void 0 : navigator.userAgent) === "Cloudflare-Workers";
function dispatchRequest(method_1, targetUrl_1, input_1) {
    return __awaiter(this, arguments, void 0, function*(method, targetUrl, input, options = {}) {
        var _a;
        const { credentials: credentialsValue, requestMiddleware, responseHandler, fetch } = (0, config_1.getConfig)();
        const userAgent = (0, runtime_1.isBrowser)() ? {} : {
            "User-Agent": (0, runtime_1.getUserAgent)()
        };
        const credentials = typeof credentialsValue === "function" ? credentialsValue() : credentialsValue;
        const { url, headers } = yield requestMiddleware({
            url: targetUrl,
            method: method.toUpperCase()
        });
        const authHeader = credentials ? {
            Authorization: `Key ${credentials}`
        } : {};
        const requestHeaders = Object.assign(Object.assign(Object.assign(Object.assign({}, authHeader), {
            Accept: "application/json",
            "Content-Type": "application/json"
        }), userAgent), headers !== null && headers !== void 0 ? headers : {});
        const { responseHandler: customResponseHandler } = options, requestInit = __rest(options, [
            "responseHandler"
        ]);
        const response = yield fetch(url, Object.assign(Object.assign(Object.assign(Object.assign({}, requestInit), {
            method,
            headers: Object.assign(Object.assign({}, requestHeaders), (_a = requestInit.headers) !== null && _a !== void 0 ? _a : {})
        }), !isCloudflareWorkers && {
            mode: "cors"
        }), {
            body: method.toLowerCase() !== "get" && input ? JSON.stringify(input) : undefined
        }));
        const handleResponse = customResponseHandler !== null && customResponseHandler !== void 0 ? customResponseHandler : responseHandler;
        return yield handleResponse(response);
    });
} //# sourceMappingURL=request.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/utils.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ensureAppIdFormat = ensureAppIdFormat;
exports.parseAppId = parseAppId;
exports.isValidUrl = isValidUrl;
exports.throttle = throttle;
exports.isReact = isReact;
exports.isPlainObject = isPlainObject;
function ensureAppIdFormat(id) {
    const parts = id.split("/");
    if (parts.length > 1) {
        return id;
    }
    const [, appOwner, appId] = /^([0-9]+)-([a-zA-Z0-9-]+)$/.exec(id) || [];
    if (appOwner && appId) {
        return `${appOwner}/${appId}`;
    }
    throw new Error(`Invalid app id: ${id}. Must be in the format <appOwner>/<appId>`);
}
const APP_NAMESPACES = [
    "workflows",
    "comfy"
];
function parseAppId(id) {
    const normalizedId = ensureAppIdFormat(id);
    const parts = normalizedId.split("/");
    if (APP_NAMESPACES.includes(parts[0])) {
        return {
            owner: parts[1],
            alias: parts[2],
            path: parts.slice(3).join("/") || undefined,
            namespace: parts[0]
        };
    }
    return {
        owner: parts[0],
        alias: parts[1],
        path: parts.slice(2).join("/") || undefined
    };
}
function isValidUrl(url) {
    try {
        const { host } = new URL(url);
        return /(fal\.(ai|run))$/.test(host);
    } catch (_) {
        return false;
    }
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function throttle(func, limit, leading = false) {
    let lastFunc;
    let lastRan;
    return (...args)=>{
        if (!lastRan && leading) {
            func(...args);
            lastRan = Date.now();
        } else {
            if (lastFunc) {
                clearTimeout(lastFunc);
            }
            lastFunc = setTimeout(()=>{
                if (Date.now() - lastRan >= limit) {
                    func(...args);
                    lastRan = Date.now();
                }
            }, limit - (Date.now() - lastRan));
        }
    };
}
let isRunningInReact;
/**
 * Not really the most optimal way to detect if we're running in React,
 * but the idea here is that we can support multiple rendering engines
 * (starting with React), with all their peculiarities, without having
 * to add a dependency or creating custom integrations (e.g. custom hooks).
 *
 * Yes, a bit of magic to make things works out-of-the-box.
 * @returns `true` if running in React, `false` otherwise.
 */ function isReact() {
    if (isRunningInReact === undefined) {
        const stack = new Error().stack;
        isRunningInReact = !!stack && (stack.includes("node_modules/react-dom/") || stack.includes("node_modules/next/"));
    }
    return isRunningInReact;
}
/**
 * Check if a value is a plain object.
 * @param value - The value to check.
 * @returns `true` if the value is a plain object, `false` otherwise.
 */ function isPlainObject(value) {
    return !!value && Object.getPrototypeOf(value) === Object.prototype;
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/storage.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.storageImpl = void 0;
const config_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/config.js [app-client] (ecmascript)");
const request_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/request.js [app-client] (ecmascript)");
const utils_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/utils.js [app-client] (ecmascript)");
/**
 * Get the file extension from the content type. This is used to generate
 * a file name if the file name is not provided.
 *
 * @param contentType the content type of the file.
 * @returns the file extension or `bin` if the content type is not recognized.
 */ function getExtensionFromContentType(contentType) {
    var _a;
    const [_, fileType] = contentType.split("/");
    return (_a = fileType.split(/[-;]/)[0]) !== null && _a !== void 0 ? _a : "bin";
}
/**
 * Initiate the upload of a file to the server. This returns the URL to upload
 * the file to and the URL of the file once it is uploaded.
 *
 * @param file the file to upload
 * @returns the URL to upload the file to and the URL of the file once it is uploaded.
 */ function initiateUpload(file) {
    return __awaiter(this, void 0, void 0, function*() {
        const contentType = file.type || "application/octet-stream";
        const filename = file.name || `${Date.now()}.${getExtensionFromContentType(contentType)}`;
        return yield (0, request_1.dispatchRequest)("POST", `${(0, config_1.getRestApiUrl)()}/storage/upload/initiate`, {
            content_type: contentType,
            file_name: filename
        });
    });
}
exports.storageImpl = {
    upload: (file)=>__awaiter(void 0, void 0, void 0, function*() {
            const { fetch } = (0, config_1.getConfig)();
            const { upload_url: uploadUrl, file_url: url } = yield initiateUpload(file);
            const response = yield fetch(uploadUrl, {
                method: "PUT",
                body: file,
                headers: {
                    "Content-Type": file.type || "application/octet-stream"
                }
            });
            const { responseHandler } = (0, config_1.getConfig)();
            yield responseHandler(response);
            return url;
        }),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    transformInput: (input)=>__awaiter(void 0, void 0, void 0, function*() {
            if (Array.isArray(input)) {
                return Promise.all(input.map((item)=>exports.storageImpl.transformInput(item)));
            } else if (input instanceof Blob) {
                return yield exports.storageImpl.upload(input);
            } else if ((0, utils_1.isPlainObject)(input)) {
                const inputObject = input;
                const promises = Object.entries(inputObject).map((_a)=>__awaiter(void 0, [
                        _a
                    ], void 0, function*([key, value]) {
                        return [
                            key,
                            (yield exports.storageImpl.transformInput(value))
                        ];
                    }));
                const results = yield Promise.all(promises);
                return Object.fromEntries(results);
            }
            // Return the input as is if it's neither an object nor a file/blob/data URI
            return input;
        })
}; //# sourceMappingURL=storage.js.map
}}),
"[project]/node_modules/eventsource-parser/dist/index.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
Object.defineProperty(exports, '__esModule', {
    value: true
});
function createParser(onParse) {
    let isFirstChunk;
    let buffer;
    let startingPosition;
    let startingFieldLength;
    let eventId;
    let eventName;
    let data;
    reset();
    return {
        feed,
        reset
    };
    "TURBOPACK unreachable";
    function reset() {
        isFirstChunk = true;
        buffer = "";
        startingPosition = 0;
        startingFieldLength = -1;
        eventId = void 0;
        eventName = void 0;
        data = "";
    }
    function feed(chunk) {
        buffer = buffer ? buffer + chunk : chunk;
        if (isFirstChunk && hasBom(buffer)) {
            buffer = buffer.slice(BOM.length);
        }
        isFirstChunk = false;
        const length = buffer.length;
        let position = 0;
        let discardTrailingNewline = false;
        while(position < length){
            if (discardTrailingNewline) {
                if (buffer[position] === "\n") {
                    ++position;
                }
                discardTrailingNewline = false;
            }
            let lineLength = -1;
            let fieldLength = startingFieldLength;
            let character;
            for(let index = startingPosition; lineLength < 0 && index < length; ++index){
                character = buffer[index];
                if (character === ":" && fieldLength < 0) {
                    fieldLength = index - position;
                } else if (character === "\r") {
                    discardTrailingNewline = true;
                    lineLength = index - position;
                } else if (character === "\n") {
                    lineLength = index - position;
                }
            }
            if (lineLength < 0) {
                startingPosition = length - position;
                startingFieldLength = fieldLength;
                break;
            } else {
                startingPosition = 0;
                startingFieldLength = -1;
            }
            parseEventStreamLine(buffer, position, fieldLength, lineLength);
            position += lineLength + 1;
        }
        if (position === length) {
            buffer = "";
        } else if (position > 0) {
            buffer = buffer.slice(position);
        }
    }
    function parseEventStreamLine(lineBuffer, index, fieldLength, lineLength) {
        if (lineLength === 0) {
            if (data.length > 0) {
                onParse({
                    type: "event",
                    id: eventId,
                    event: eventName || void 0,
                    data: data.slice(0, -1)
                });
                data = "";
                eventId = void 0;
            }
            eventName = void 0;
            return;
        }
        const noValue = fieldLength < 0;
        const field = lineBuffer.slice(index, index + (noValue ? lineLength : fieldLength));
        let step = 0;
        if (noValue) {
            step = lineLength;
        } else if (lineBuffer[index + fieldLength + 1] === " ") {
            step = fieldLength + 2;
        } else {
            step = fieldLength + 1;
        }
        const position = index + step;
        const valueLength = lineLength - step;
        const value = lineBuffer.slice(position, position + valueLength).toString();
        if (field === "data") {
            data += value ? "".concat(value, "\n") : "\n";
        } else if (field === "event") {
            eventName = value;
        } else if (field === "id" && !value.includes("\0")) {
            eventId = value;
        } else if (field === "retry") {
            const retry = parseInt(value, 10);
            if (!Number.isNaN(retry)) {
                onParse({
                    type: "reconnect-interval",
                    value: retry
                });
            }
        }
    }
}
const BOM = [
    239,
    187,
    191
];
function hasBom(buffer) {
    return BOM.every((charCode, index)=>buffer.charCodeAt(index) === charCode);
}
exports.createParser = createParser; //# sourceMappingURL=index.cjs.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/auth.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.TOKEN_EXPIRATION_SECONDS = void 0;
exports.getTemporaryAuthToken = getTemporaryAuthToken;
const config_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/config.js [app-client] (ecmascript)");
const request_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/request.js [app-client] (ecmascript)");
const utils_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/utils.js [app-client] (ecmascript)");
exports.TOKEN_EXPIRATION_SECONDS = 120;
/**
 * Get a token to connect to the realtime endpoint.
 */ function getTemporaryAuthToken(app) {
    return __awaiter(this, void 0, void 0, function*() {
        const appId = (0, utils_1.parseAppId)(app);
        const token = yield (0, request_1.dispatchRequest)("POST", `${(0, config_1.getRestApiUrl)()}/tokens/`, {
            allowed_apps: [
                appId.alias
            ],
            token_expiration: exports.TOKEN_EXPIRATION_SECONDS
        });
        // keep this in case the response was wrapped (old versions of the proxy do that)
        // should be safe to remove in the future
        if (typeof token !== "string" && token["detail"]) {
            return token["detail"];
        }
        return token;
    });
} //# sourceMappingURL=auth.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/streaming.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __await = this && this.__await || function(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncGenerator = this && this.__asyncGenerator || function(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = {}, verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.FalStream = void 0;
exports.stream = stream;
const eventsource_parser_1 = __turbopack_context__.r("[project]/node_modules/eventsource-parser/dist/index.cjs [app-client] (ecmascript)");
const auth_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/auth.js [app-client] (ecmascript)");
const config_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/config.js [app-client] (ecmascript)");
const function_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/function.js [app-client] (ecmascript)");
const request_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/request.js [app-client] (ecmascript)");
const response_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/response.js [app-client] (ecmascript)");
const storage_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/storage.js [app-client] (ecmascript)");
const CONTENT_TYPE_EVENT_STREAM = "text/event-stream";
const EVENT_STREAM_TIMEOUT = 15 * 1000;
/**
 * The class representing a streaming response. With t
 */ class FalStream {
    constructor(endpointId, options){
        var _a;
        // support for event listeners
        this.listeners = new Map();
        this.buffer = [];
        // local state
        this.currentData = undefined;
        this.lastEventTimestamp = 0;
        this.streamClosed = false;
        this.abortController = new AbortController();
        this.start = ()=>__awaiter(this, void 0, void 0, function*() {
                var _a, _b;
                const { endpointId, options } = this;
                const { input, method = "post", connectionMode = "server" } = options;
                try {
                    if (connectionMode === "client") {
                        // if we are in the browser, we need to get a temporary token
                        // to authenticate the request
                        const token = yield (0, auth_1.getTemporaryAuthToken)(endpointId);
                        const { fetch } = (0, config_1.getConfig)();
                        const parsedUrl = new URL(this.url);
                        parsedUrl.searchParams.set("fal_jwt_token", token);
                        const response = yield fetch(parsedUrl.toString(), {
                            method: method.toUpperCase(),
                            headers: {
                                accept: (_a = options.accept) !== null && _a !== void 0 ? _a : CONTENT_TYPE_EVENT_STREAM,
                                "content-type": "application/json"
                            },
                            body: input && method !== "get" ? JSON.stringify(input) : undefined,
                            signal: this.abortController.signal
                        });
                        return yield this.handleResponse(response);
                    }
                    return yield (0, request_1.dispatchRequest)(method.toUpperCase(), this.url, input, {
                        headers: {
                            accept: (_b = options.accept) !== null && _b !== void 0 ? _b : CONTENT_TYPE_EVENT_STREAM
                        },
                        responseHandler: this.handleResponse,
                        signal: this.abortController.signal
                    });
                } catch (error) {
                    this.handleError(error);
                }
            });
        this.handleResponse = (response)=>__awaiter(this, void 0, void 0, function*() {
                var _a;
                if (!response.ok) {
                    try {
                        // we know the response failed, call the response handler
                        // so the exception gets converted to ApiError correctly
                        yield (0, response_1.defaultResponseHandler)(response);
                    } catch (error) {
                        this.emit("error", error);
                    }
                    return;
                }
                const body = response.body;
                if (!body) {
                    this.emit("error", new response_1.ApiError({
                        message: "Response body is empty.",
                        status: 400,
                        body: undefined
                    }));
                    return;
                }
                const isEventStream = response.headers.get("content-type").startsWith(CONTENT_TYPE_EVENT_STREAM);
                // any response that is not a text/event-stream will be handled as a binary stream
                if (!isEventStream) {
                    const reader = body.getReader();
                    const emitRawChunk = ()=>{
                        reader.read().then(({ done, value })=>{
                            if (done) {
                                this.emit("done", this.currentData);
                                return;
                            }
                            this.currentData = value;
                            this.emit("data", value);
                            emitRawChunk();
                        });
                    };
                    emitRawChunk();
                    return;
                }
                const decoder = new TextDecoder("utf-8");
                const reader = response.body.getReader();
                const parser = (0, eventsource_parser_1.createParser)((event)=>{
                    if (event.type === "event") {
                        const data = event.data;
                        try {
                            const parsedData = JSON.parse(data);
                            this.buffer.push(parsedData);
                            this.currentData = parsedData;
                            this.emit("data", parsedData);
                            // also emit 'message'for backwards compatibility
                            this.emit("message", parsedData);
                        } catch (e) {
                            this.emit("error", e);
                        }
                    }
                });
                const timeout = (_a = this.options.timeout) !== null && _a !== void 0 ? _a : EVENT_STREAM_TIMEOUT;
                const readPartialResponse = ()=>__awaiter(this, void 0, void 0, function*() {
                        const { value, done } = yield reader.read();
                        this.lastEventTimestamp = Date.now();
                        parser.feed(decoder.decode(value));
                        if (Date.now() - this.lastEventTimestamp > timeout) {
                            this.emit("error", new response_1.ApiError({
                                message: `Event stream timed out after ${(timeout / 1000).toFixed(0)} seconds with no messages.`,
                                status: 408
                            }));
                        }
                        if (!done) {
                            readPartialResponse().catch(this.handleError);
                        } else {
                            this.emit("done", this.currentData);
                        }
                    });
                readPartialResponse().catch(this.handleError);
                return;
            });
        this.handleError = (error)=>{
            var _a;
            const apiError = error instanceof response_1.ApiError ? error : new response_1.ApiError({
                message: (_a = error.message) !== null && _a !== void 0 ? _a : "An unknown error occurred",
                status: 500
            });
            this.emit("error", apiError);
            return;
        };
        this.on = (type, listener)=>{
            var _a;
            if (!this.listeners.has(type)) {
                this.listeners.set(type, []);
            }
            (_a = this.listeners.get(type)) === null || _a === void 0 ? void 0 : _a.push(listener);
        };
        this.emit = (type, event)=>{
            const listeners = this.listeners.get(type) || [];
            for (const listener of listeners){
                listener(event);
            }
        };
        /**
         * Gets a reference to the `Promise` that indicates whether the streaming
         * is done or not. Developers should always call this in their apps to ensure
         * the request is over.
         *
         * An alternative to this, is to use `on('done')` in case your application
         * architecture works best with event listeners.
         *
         * @returns the promise that resolves when the request is done.
         */ this.done = ()=>__awaiter(this, void 0, void 0, function*() {
                return this.donePromise;
            });
        /**
         * Aborts the streaming request.
         */ this.abort = ()=>{
            this.abortController.abort();
        };
        this.endpointId = endpointId;
        this.url = (_a = options.url) !== null && _a !== void 0 ? _a : (0, function_1.buildUrl)(endpointId, {
            path: "/stream",
            query: options.queryParams
        });
        this.options = options;
        this.donePromise = new Promise((resolve, reject)=>{
            if (this.streamClosed) {
                reject(new response_1.ApiError({
                    message: "Streaming connection is already closed.",
                    status: 400,
                    body: undefined
                }));
            }
            this.on("done", (data)=>{
                this.streamClosed = true;
                resolve(data);
            });
            this.on("error", (error)=>{
                this.streamClosed = true;
                reject(error);
            });
        });
        this.start().catch(this.handleError);
    }
    [Symbol.asyncIterator]() {
        return __asyncGenerator(this, arguments, function* _a() {
            let running = true;
            const stopAsyncIterator = ()=>running = false;
            this.on("error", stopAsyncIterator);
            this.on("done", stopAsyncIterator);
            while(running){
                const data = this.buffer.shift();
                if (data) {
                    yield yield __await(data);
                }
                // the short timeout ensures the while loop doesn't block other
                // frames getting executed concurrently
                yield __await(new Promise((resolve)=>setTimeout(resolve, 16)));
            }
        });
    }
}
exports.FalStream = FalStream;
/**
 * Calls a fal app that supports streaming and provides a streaming-capable
 * object as a result, that can be used to get partial results through either
 * `AsyncIterator` or through an event listener.
 *
 * @param endpointId the endpoint id, e.g. `fal-ai/llavav15-13b`.
 * @param options the request options, including the input payload.
 * @returns the `FalStream` instance.
 */ function stream(endpointId, options) {
    return __awaiter(this, void 0, void 0, function*() {
        const input = options.input && options.autoUpload !== false ? yield storage_1.storageImpl.transformInput(options.input) : options.input;
        return new FalStream(endpointId, Object.assign(Object.assign({}, options), {
            input: input
        }));
    });
} //# sourceMappingURL=streaming.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/function.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.queue = void 0;
exports.buildUrl = buildUrl;
exports.send = send;
exports.run = run;
exports.subscribe = subscribe;
const request_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/request.js [app-client] (ecmascript)");
const storage_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/storage.js [app-client] (ecmascript)");
const streaming_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/streaming.js [app-client] (ecmascript)");
const utils_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/utils.js [app-client] (ecmascript)");
/**
 * Builds the final url to run the function based on its `id` or alias and
 * a the options from `RunOptions<Input>`.
 *
 * @private
 * @param id the function id or alias
 * @param options the run options
 * @returns the final url to run the function
 */ function buildUrl(id, options = {}) {
    var _a, _b;
    const method = ((_a = options.method) !== null && _a !== void 0 ? _a : "post").toLowerCase();
    const path = ((_b = options.path) !== null && _b !== void 0 ? _b : "").replace(/^\//, "").replace(/\/{2,}/, "/");
    const input = options.input;
    const params = Object.assign(Object.assign({}, options.query || {}), method === "get" ? input : {});
    const queryParams = Object.keys(params).length > 0 ? `?${new URLSearchParams(params).toString()}` : "";
    // if a fal url is passed, just use it
    if ((0, utils_1.isValidUrl)(id)) {
        const url = id.endsWith("/") ? id : `${id}/`;
        return `${url}${path}${queryParams}`;
    }
    const appId = (0, utils_1.ensureAppIdFormat)(id);
    const subdomain = options.subdomain ? `${options.subdomain}.` : "";
    const url = `https://${subdomain}fal.run/${appId}/${path}`;
    return `${url.replace(/\/$/, "")}${queryParams}`;
}
function send(id_1) {
    return __awaiter(this, arguments, void 0, function*(id, options = {}) {
        var _a;
        const input = options.input && options.autoUpload !== false ? yield storage_1.storageImpl.transformInput(options.input) : options.input;
        return (0, request_1.dispatchRequest)((_a = options.method) !== null && _a !== void 0 ? _a : "post", buildUrl(id, options), input);
    });
}
/**
 * Runs a fal serverless function identified by its `id`.
 *
 * @param id the registered function revision id or alias.
 * @returns the remote function output
 */ function run(id_1) {
    return __awaiter(this, arguments, void 0, function*(id, options = {}) {
        return send(id, options);
    });
}
const DEFAULT_POLL_INTERVAL = 500;
/**
 * The fal run queue module. It allows to submit a function to the queue and get its result
 * on a separate call. This is useful for long running functions that can be executed
 * asynchronously and not .
 */ exports.queue = {
    submit (endpointId, options) {
        return __awaiter(this, void 0, void 0, function*() {
            const { webhookUrl, path = "" } = options, runOptions = __rest(options, [
                "webhookUrl",
                "path"
            ]);
            return send(endpointId, Object.assign(Object.assign({}, runOptions), {
                subdomain: "queue",
                method: "post",
                path: path,
                query: webhookUrl ? {
                    fal_webhook: webhookUrl
                } : undefined
            }));
        });
    },
    status (endpointId_1, _a) {
        return __awaiter(this, arguments, void 0, function*(endpointId, { requestId, logs = false }) {
            const appId = (0, utils_1.parseAppId)(endpointId);
            const prefix = appId.namespace ? `${appId.namespace}/` : "";
            return send(`${prefix}${appId.owner}/${appId.alias}`, {
                subdomain: "queue",
                method: "get",
                path: `/requests/${requestId}/status`,
                input: {
                    logs: logs ? "1" : "0"
                }
            });
        });
    },
    streamStatus (endpointId_1, _a) {
        return __awaiter(this, arguments, void 0, function*(endpointId, { requestId, logs = false, connectionMode }) {
            const appId = (0, utils_1.parseAppId)(endpointId);
            const prefix = appId.namespace ? `${appId.namespace}/` : "";
            const queryParams = {
                logs: logs ? "1" : "0"
            };
            const url = buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {
                subdomain: "queue",
                path: `/requests/${requestId}/status/stream`,
                query: queryParams
            });
            return new streaming_1.FalStream(endpointId, {
                url,
                method: "get",
                connectionMode,
                queryParams
            });
        });
    },
    subscribeToStatus (endpointId, options) {
        return __awaiter(this, void 0, void 0, function*() {
            const requestId = options.requestId;
            const timeout = options.timeout;
            let timeoutId = undefined;
            const handleCancelError = ()=>{
            // Ignore errors as the client will follow through with the timeout
            // regardless of the server response. In case cancelation fails, we
            // still want to reject the promise and consider the client call canceled.
            };
            if (options.mode === "streaming") {
                const status = yield exports.queue.streamStatus(endpointId, {
                    requestId,
                    logs: options.logs,
                    connectionMode: "connectionMode" in options ? options.connectionMode : undefined
                });
                const logs = [];
                if (timeout) {
                    timeoutId = setTimeout(()=>{
                        status.abort();
                        exports.queue.cancel(endpointId, {
                            requestId
                        }).catch(handleCancelError);
                        // TODO this error cannot bubble up to the user since it's thrown in
                        // a closure in the global scope due to setTimeout behavior.
                        // User will get a platform error instead. We should find a way to
                        // make this behavior aligned with polling.
                        throw new Error(`Client timed out waiting for the request to complete after ${timeout}ms`);
                    }, timeout);
                }
                status.on("data", (data)=>{
                    if (options.onQueueUpdate) {
                        // accumulate logs to match previous polling behavior
                        if ("logs" in data && Array.isArray(data.logs) && data.logs.length > 0) {
                            logs.push(...data.logs);
                        }
                        options.onQueueUpdate("logs" in data ? Object.assign(Object.assign({}, data), {
                            logs
                        }) : data);
                    }
                });
                const doneStatus = yield status.done();
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                return doneStatus;
            }
            // default to polling until status streaming is stable and faster
            return new Promise((resolve, reject)=>{
                var _a;
                let pollingTimeoutId;
                // type resolution isn't great in this case, so check for its presence
                // and and type so the typechecker behaves as expected
                const pollInterval = "pollInterval" in options && typeof options.pollInterval === "number" ? (_a = options.pollInterval) !== null && _a !== void 0 ? _a : DEFAULT_POLL_INTERVAL : DEFAULT_POLL_INTERVAL;
                const clearScheduledTasks = ()=>{
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                    if (pollingTimeoutId) {
                        clearTimeout(pollingTimeoutId);
                    }
                };
                if (timeout) {
                    timeoutId = setTimeout(()=>{
                        clearScheduledTasks();
                        exports.queue.cancel(endpointId, {
                            requestId
                        }).catch(handleCancelError);
                        reject(new Error(`Client timed out waiting for the request to complete after ${timeout}ms`));
                    }, timeout);
                }
                const poll = ()=>__awaiter(this, void 0, void 0, function*() {
                        var _a;
                        try {
                            const requestStatus = yield exports.queue.status(endpointId, {
                                requestId,
                                logs: (_a = options.logs) !== null && _a !== void 0 ? _a : false
                            });
                            if (options.onQueueUpdate) {
                                options.onQueueUpdate(requestStatus);
                            }
                            if (requestStatus.status === "COMPLETED") {
                                clearScheduledTasks();
                                resolve(requestStatus);
                                return;
                            }
                            pollingTimeoutId = setTimeout(poll, pollInterval);
                        } catch (error) {
                            clearScheduledTasks();
                            reject(error);
                        }
                    });
                poll().catch(reject);
            });
        });
    },
    result (endpointId_1, _a) {
        return __awaiter(this, arguments, void 0, function*(endpointId, { requestId }) {
            const appId = (0, utils_1.parseAppId)(endpointId);
            const prefix = appId.namespace ? `${appId.namespace}/` : "";
            return send(`${prefix}${appId.owner}/${appId.alias}`, {
                subdomain: "queue",
                method: "get",
                path: `/requests/${requestId}`
            });
        });
    },
    cancel (endpointId_1, _a) {
        return __awaiter(this, arguments, void 0, function*(endpointId, { requestId }) {
            const appId = (0, utils_1.parseAppId)(endpointId);
            const prefix = appId.namespace ? `${appId.namespace}/` : "";
            yield send(`${prefix}${appId.owner}/${appId.alias}`, {
                subdomain: "queue",
                method: "put",
                path: `/requests/${requestId}/cancel`
            });
        });
    }
};
/**
 * Subscribes to updates for a specific request in the queue.
 *
 * @param endpointId - The ID of the function web endpoint.
 * @param options - Options to configure how the request is run and how updates are received.
 * @returns A promise that resolves to the result of the request once it's completed.
 */ function subscribe(endpointId_1) {
    return __awaiter(this, arguments, void 0, function*(endpointId, options = {}) {
        const { request_id: requestId } = yield exports.queue.submit(endpointId, options);
        if (options.onEnqueue) {
            options.onEnqueue(requestId);
        }
        yield exports.queue.subscribeToStatus(endpointId, Object.assign({
            requestId
        }, options));
        return exports.queue.result(endpointId, {
            requestId
        });
    });
} //# sourceMappingURL=function.js.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Main Functions:
__turbopack_context__.s({});
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <locals>");
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "utf8Count": (()=>utf8Count),
    "utf8Decode": (()=>utf8Decode),
    "utf8DecodeJs": (()=>utf8DecodeJs),
    "utf8DecodeTD": (()=>utf8DecodeTD),
    "utf8Encode": (()=>utf8Encode),
    "utf8EncodeJs": (()=>utf8EncodeJs),
    "utf8EncodeTE": (()=>utf8EncodeTE)
});
function utf8Count(str) {
    const strLength = str.length;
    let byteLength = 0;
    let pos = 0;
    while(pos < strLength){
        let value = str.charCodeAt(pos++);
        if ((value & 0xffffff80) === 0) {
            // 1-byte
            byteLength++;
            continue;
        } else if ((value & 0xfffff800) === 0) {
            // 2-bytes
            byteLength += 2;
        } else {
            // handle surrogate pair
            if (value >= 0xd800 && value <= 0xdbff) {
                // high surrogate
                if (pos < strLength) {
                    const extra = str.charCodeAt(pos);
                    if ((extra & 0xfc00) === 0xdc00) {
                        ++pos;
                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;
                    }
                }
            }
            if ((value & 0xffff0000) === 0) {
                // 3-byte
                byteLength += 3;
            } else {
                // 4-byte
                byteLength += 4;
            }
        }
    }
    return byteLength;
}
function utf8EncodeJs(str, output, outputOffset) {
    const strLength = str.length;
    let offset = outputOffset;
    let pos = 0;
    while(pos < strLength){
        let value = str.charCodeAt(pos++);
        if ((value & 0xffffff80) === 0) {
            // 1-byte
            output[offset++] = value;
            continue;
        } else if ((value & 0xfffff800) === 0) {
            // 2-bytes
            output[offset++] = value >> 6 & 0x1f | 0xc0;
        } else {
            // handle surrogate pair
            if (value >= 0xd800 && value <= 0xdbff) {
                // high surrogate
                if (pos < strLength) {
                    const extra = str.charCodeAt(pos);
                    if ((extra & 0xfc00) === 0xdc00) {
                        ++pos;
                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;
                    }
                }
            }
            if ((value & 0xffff0000) === 0) {
                // 3-byte
                output[offset++] = value >> 12 & 0x0f | 0xe0;
                output[offset++] = value >> 6 & 0x3f | 0x80;
            } else {
                // 4-byte
                output[offset++] = value >> 18 & 0x07 | 0xf0;
                output[offset++] = value >> 12 & 0x3f | 0x80;
                output[offset++] = value >> 6 & 0x3f | 0x80;
            }
        }
        output[offset++] = value & 0x3f | 0x80;
    }
}
// TextEncoder and TextDecoder are standardized in whatwg encoding:
// https://encoding.spec.whatwg.org/
// and available in all the modern browsers:
// https://caniuse.com/textencoder
// They are available in Node.js since v12 LTS as well:
// https://nodejs.org/api/globals.html#textencoder
const sharedTextEncoder = new TextEncoder();
// This threshold should be determined by benchmarking, which might vary in engines and input data.
// Run `npx ts-node benchmark/encode-string.ts` for details.
const TEXT_ENCODER_THRESHOLD = 50;
function utf8EncodeTE(str, output, outputOffset) {
    sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));
}
function utf8Encode(str, output, outputOffset) {
    if (str.length > TEXT_ENCODER_THRESHOLD) {
        utf8EncodeTE(str, output, outputOffset);
    } else {
        utf8EncodeJs(str, output, outputOffset);
    }
}
const CHUNK_SIZE = 4096;
function utf8DecodeJs(bytes, inputOffset, byteLength) {
    let offset = inputOffset;
    const end = offset + byteLength;
    const units = [];
    let result = "";
    while(offset < end){
        const byte1 = bytes[offset++];
        if ((byte1 & 0x80) === 0) {
            // 1 byte
            units.push(byte1);
        } else if ((byte1 & 0xe0) === 0xc0) {
            // 2 bytes
            const byte2 = bytes[offset++] & 0x3f;
            units.push((byte1 & 0x1f) << 6 | byte2);
        } else if ((byte1 & 0xf0) === 0xe0) {
            // 3 bytes
            const byte2 = bytes[offset++] & 0x3f;
            const byte3 = bytes[offset++] & 0x3f;
            units.push((byte1 & 0x1f) << 12 | byte2 << 6 | byte3);
        } else if ((byte1 & 0xf8) === 0xf0) {
            // 4 bytes
            const byte2 = bytes[offset++] & 0x3f;
            const byte3 = bytes[offset++] & 0x3f;
            const byte4 = bytes[offset++] & 0x3f;
            let unit = (byte1 & 0x07) << 0x12 | byte2 << 0x0c | byte3 << 0x06 | byte4;
            if (unit > 0xffff) {
                unit -= 0x10000;
                units.push(unit >>> 10 & 0x3ff | 0xd800);
                unit = 0xdc00 | unit & 0x3ff;
            }
            units.push(unit);
        } else {
            units.push(byte1);
        }
        if (units.length >= CHUNK_SIZE) {
            result += String.fromCharCode(...units);
            units.length = 0;
        }
    }
    if (units.length > 0) {
        result += String.fromCharCode(...units);
    }
    return result;
}
const sharedTextDecoder = new TextDecoder();
// This threshold should be determined by benchmarking, which might vary in engines and input data.
// Run `npx ts-node benchmark/decode-string.ts` for details.
const TEXT_DECODER_THRESHOLD = 200;
function utf8DecodeTD(bytes, inputOffset, byteLength) {
    const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);
    return sharedTextDecoder.decode(stringBytes);
}
function utf8Decode(bytes, inputOffset, byteLength) {
    if (byteLength > TEXT_DECODER_THRESHOLD) {
        return utf8DecodeTD(bytes, inputOffset, byteLength);
    } else {
        return utf8DecodeJs(bytes, inputOffset, byteLength);
    }
} //# sourceMappingURL=utf8.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.
 */ __turbopack_context__.s({
    "ExtData": (()=>ExtData)
});
class ExtData {
    constructor(type, data){
        this.type = type;
        this.data = data;
    }
} //# sourceMappingURL=ExtData.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DecodeError": (()=>DecodeError)
});
class DecodeError extends Error {
    constructor(message){
        super(message);
        // fix the prototype chain in a cross-platform way
        const proto = Object.create(DecodeError.prototype);
        Object.setPrototypeOf(this, proto);
        Object.defineProperty(this, "name", {
            configurable: true,
            enumerable: false,
            value: DecodeError.name
        });
    }
} //# sourceMappingURL=DecodeError.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Integer Utility
__turbopack_context__.s({
    "UINT32_MAX": (()=>UINT32_MAX),
    "getInt64": (()=>getInt64),
    "getUint64": (()=>getUint64),
    "setInt64": (()=>setInt64),
    "setUint64": (()=>setUint64)
});
const UINT32_MAX = 4294967295;
function setUint64(view, offset, value) {
    const high = value / 4294967296;
    const low = value; // high bits are truncated by DataView
    view.setUint32(offset, high);
    view.setUint32(offset + 4, low);
}
function setInt64(view, offset, value) {
    const high = Math.floor(value / 4294967296);
    const low = value; // high bits are truncated by DataView
    view.setUint32(offset, high);
    view.setUint32(offset + 4, low);
}
function getInt64(view, offset) {
    const high = view.getInt32(offset);
    const low = view.getUint32(offset + 4);
    return high * 4294967296 + low;
}
function getUint64(view, offset) {
    const high = view.getUint32(offset);
    const low = view.getUint32(offset + 4);
    return high * 4294967296 + low;
} //# sourceMappingURL=int.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type
__turbopack_context__.s({
    "EXT_TIMESTAMP": (()=>EXT_TIMESTAMP),
    "decodeTimestampExtension": (()=>decodeTimestampExtension),
    "decodeTimestampToTimeSpec": (()=>decodeTimestampToTimeSpec),
    "encodeDateToTimeSpec": (()=>encodeDateToTimeSpec),
    "encodeTimeSpecToTimestamp": (()=>encodeTimeSpecToTimestamp),
    "encodeTimestampExtension": (()=>encodeTimestampExtension),
    "timestampExtension": (()=>timestampExtension)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs [app-client] (ecmascript)");
;
;
const EXT_TIMESTAMP = -1;
const TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int
const TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int
function encodeTimeSpecToTimestamp({ sec, nsec }) {
    if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {
        // Here sec >= 0 && nsec >= 0
        if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {
            // timestamp 32 = { sec32 (unsigned) }
            const rv = new Uint8Array(4);
            const view = new DataView(rv.buffer);
            view.setUint32(0, sec);
            return rv;
        } else {
            // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }
            const secHigh = sec / 0x100000000;
            const secLow = sec & 0xffffffff;
            const rv = new Uint8Array(8);
            const view = new DataView(rv.buffer);
            // nsec30 | secHigh2
            view.setUint32(0, nsec << 2 | secHigh & 0x3);
            // secLow32
            view.setUint32(4, secLow);
            return rv;
        }
    } else {
        // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }
        const rv = new Uint8Array(12);
        const view = new DataView(rv.buffer);
        view.setUint32(0, nsec);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setInt64"])(view, 4, sec);
        return rv;
    }
}
function encodeDateToTimeSpec(date) {
    const msec = date.getTime();
    const sec = Math.floor(msec / 1e3);
    const nsec = (msec - sec * 1e3) * 1e6;
    // Normalizes { sec, nsec } to ensure nsec is unsigned.
    const nsecInSec = Math.floor(nsec / 1e9);
    return {
        sec: sec + nsecInSec,
        nsec: nsec - nsecInSec * 1e9
    };
}
function encodeTimestampExtension(object) {
    if (object instanceof Date) {
        const timeSpec = encodeDateToTimeSpec(object);
        return encodeTimeSpecToTimestamp(timeSpec);
    } else {
        return null;
    }
}
function decodeTimestampToTimeSpec(data) {
    const view = new DataView(data.buffer, data.byteOffset, data.byteLength);
    // data may be 32, 64, or 96 bits
    switch(data.byteLength){
        case 4:
            {
                // timestamp 32 = { sec32 }
                const sec = view.getUint32(0);
                const nsec = 0;
                return {
                    sec,
                    nsec
                };
            }
        case 8:
            {
                // timestamp 64 = { nsec30, sec34 }
                const nsec30AndSecHigh2 = view.getUint32(0);
                const secLow32 = view.getUint32(4);
                const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;
                const nsec = nsec30AndSecHigh2 >>> 2;
                return {
                    sec,
                    nsec
                };
            }
        case 12:
            {
                // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }
                const sec = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInt64"])(view, 4);
                const nsec = view.getUint32(0);
                return {
                    sec,
                    nsec
                };
            }
        default:
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);
    }
}
function decodeTimestampExtension(data) {
    const timeSpec = decodeTimestampToTimeSpec(data);
    return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);
}
const timestampExtension = {
    type: EXT_TIMESTAMP,
    encode: encodeTimestampExtension,
    decode: decodeTimestampExtension
}; //# sourceMappingURL=timestamp.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// ExtensionCodec to handle MessagePack extensions
__turbopack_context__.s({
    "ExtensionCodec": (()=>ExtensionCodec)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs [app-client] (ecmascript)");
;
;
class ExtensionCodec {
    constructor(){
        // built-in extensions
        this.builtInEncoders = [];
        this.builtInDecoders = [];
        // custom extensions
        this.encoders = [];
        this.decoders = [];
        this.register(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["timestampExtension"]);
    }
    register({ type, encode, decode }) {
        if (type >= 0) {
            // custom extensions
            this.encoders[type] = encode;
            this.decoders[type] = decode;
        } else {
            // built-in extensions
            const index = -1 - type;
            this.builtInEncoders[index] = encode;
            this.builtInDecoders[index] = decode;
        }
    }
    tryToEncode(object, context) {
        // built-in extensions
        for(let i = 0; i < this.builtInEncoders.length; i++){
            const encodeExt = this.builtInEncoders[i];
            if (encodeExt != null) {
                const data = encodeExt(object, context);
                if (data != null) {
                    const type = -1 - i;
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtData"](type, data);
                }
            }
        }
        // custom extensions
        for(let i = 0; i < this.encoders.length; i++){
            const encodeExt = this.encoders[i];
            if (encodeExt != null) {
                const data = encodeExt(object, context);
                if (data != null) {
                    const type = i;
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtData"](type, data);
                }
            }
        }
        if (object instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtData"]) {
            // to keep ExtData as is
            return object;
        }
        return null;
    }
    decode(data, type, context) {
        const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];
        if (decodeExt) {
            return decodeExt(data, type, context);
        } else {
            // decode() does not fail, returns ExtData instead.
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtData"](type, data);
        }
    }
}
ExtensionCodec.defaultCodec = new ExtensionCodec(); //# sourceMappingURL=ExtensionCodec.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ensureUint8Array": (()=>ensureUint8Array)
});
function isArrayBufferLike(buffer) {
    return buffer instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && buffer instanceof SharedArrayBuffer;
}
function ensureUint8Array(buffer) {
    if (buffer instanceof Uint8Array) {
        return buffer;
    } else if (ArrayBuffer.isView(buffer)) {
        return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);
    } else if (isArrayBufferLike(buffer)) {
        return new Uint8Array(buffer);
    } else {
        // ArrayLike<number>
        return Uint8Array.from(buffer);
    }
} //# sourceMappingURL=typedArrays.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DEFAULT_INITIAL_BUFFER_SIZE": (()=>DEFAULT_INITIAL_BUFFER_SIZE),
    "DEFAULT_MAX_DEPTH": (()=>DEFAULT_MAX_DEPTH),
    "Encoder": (()=>Encoder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtensionCodec$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$typedArrays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs [app-client] (ecmascript)");
;
;
;
;
const DEFAULT_MAX_DEPTH = 100;
const DEFAULT_INITIAL_BUFFER_SIZE = 2048;
class Encoder {
    constructor(options){
        this.entered = false;
        this.extensionCodec = options?.extensionCodec ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtensionCodec$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtensionCodec"].defaultCodec;
        this.context = options?.context; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined
        this.useBigInt64 = options?.useBigInt64 ?? false;
        this.maxDepth = options?.maxDepth ?? DEFAULT_MAX_DEPTH;
        this.initialBufferSize = options?.initialBufferSize ?? DEFAULT_INITIAL_BUFFER_SIZE;
        this.sortKeys = options?.sortKeys ?? false;
        this.forceFloat32 = options?.forceFloat32 ?? false;
        this.ignoreUndefined = options?.ignoreUndefined ?? false;
        this.forceIntegerToFloat = options?.forceIntegerToFloat ?? false;
        this.pos = 0;
        this.view = new DataView(new ArrayBuffer(this.initialBufferSize));
        this.bytes = new Uint8Array(this.view.buffer);
    }
    clone() {
        // Because of slightly special argument `context`,
        // type assertion is needed.
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        return new Encoder({
            extensionCodec: this.extensionCodec,
            context: this.context,
            useBigInt64: this.useBigInt64,
            maxDepth: this.maxDepth,
            initialBufferSize: this.initialBufferSize,
            sortKeys: this.sortKeys,
            forceFloat32: this.forceFloat32,
            ignoreUndefined: this.ignoreUndefined,
            forceIntegerToFloat: this.forceIntegerToFloat
        });
    }
    reinitializeState() {
        this.pos = 0;
    }
    /**
     * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.
     *
     * @returns Encodes the object and returns a shared reference the encoder's internal buffer.
     */ encodeSharedRef(object) {
        if (this.entered) {
            const instance = this.clone();
            return instance.encodeSharedRef(object);
        }
        try {
            this.entered = true;
            this.reinitializeState();
            this.doEncode(object, 1);
            return this.bytes.subarray(0, this.pos);
        } finally{
            this.entered = false;
        }
    }
    /**
     * @returns Encodes the object and returns a copy of the encoder's internal buffer.
     */ encode(object) {
        if (this.entered) {
            const instance = this.clone();
            return instance.encode(object);
        }
        try {
            this.entered = true;
            this.reinitializeState();
            this.doEncode(object, 1);
            return this.bytes.slice(0, this.pos);
        } finally{
            this.entered = false;
        }
    }
    doEncode(object, depth) {
        if (depth > this.maxDepth) {
            throw new Error(`Too deep objects in depth ${depth}`);
        }
        if (object == null) {
            this.encodeNil();
        } else if (typeof object === "boolean") {
            this.encodeBoolean(object);
        } else if (typeof object === "number") {
            if (!this.forceIntegerToFloat) {
                this.encodeNumber(object);
            } else {
                this.encodeNumberAsFloat(object);
            }
        } else if (typeof object === "string") {
            this.encodeString(object);
        } else if (this.useBigInt64 && typeof object === "bigint") {
            this.encodeBigInt64(object);
        } else {
            this.encodeObject(object, depth);
        }
    }
    ensureBufferSizeToWrite(sizeToWrite) {
        const requiredSize = this.pos + sizeToWrite;
        if (this.view.byteLength < requiredSize) {
            this.resizeBuffer(requiredSize * 2);
        }
    }
    resizeBuffer(newSize) {
        const newBuffer = new ArrayBuffer(newSize);
        const newBytes = new Uint8Array(newBuffer);
        const newView = new DataView(newBuffer);
        newBytes.set(this.bytes);
        this.view = newView;
        this.bytes = newBytes;
    }
    encodeNil() {
        this.writeU8(0xc0);
    }
    encodeBoolean(object) {
        if (object === false) {
            this.writeU8(0xc2);
        } else {
            this.writeU8(0xc3);
        }
    }
    encodeNumber(object) {
        if (!this.forceIntegerToFloat && Number.isSafeInteger(object)) {
            if (object >= 0) {
                if (object < 0x80) {
                    // positive fixint
                    this.writeU8(object);
                } else if (object < 0x100) {
                    // uint 8
                    this.writeU8(0xcc);
                    this.writeU8(object);
                } else if (object < 0x10000) {
                    // uint 16
                    this.writeU8(0xcd);
                    this.writeU16(object);
                } else if (object < 0x100000000) {
                    // uint 32
                    this.writeU8(0xce);
                    this.writeU32(object);
                } else if (!this.useBigInt64) {
                    // uint 64
                    this.writeU8(0xcf);
                    this.writeU64(object);
                } else {
                    this.encodeNumberAsFloat(object);
                }
            } else {
                if (object >= -0x20) {
                    // negative fixint
                    this.writeU8(0xe0 | object + 0x20);
                } else if (object >= -0x80) {
                    // int 8
                    this.writeU8(0xd0);
                    this.writeI8(object);
                } else if (object >= -0x8000) {
                    // int 16
                    this.writeU8(0xd1);
                    this.writeI16(object);
                } else if (object >= -0x80000000) {
                    // int 32
                    this.writeU8(0xd2);
                    this.writeI32(object);
                } else if (!this.useBigInt64) {
                    // int 64
                    this.writeU8(0xd3);
                    this.writeI64(object);
                } else {
                    this.encodeNumberAsFloat(object);
                }
            }
        } else {
            this.encodeNumberAsFloat(object);
        }
    }
    encodeNumberAsFloat(object) {
        if (this.forceFloat32) {
            // float 32
            this.writeU8(0xca);
            this.writeF32(object);
        } else {
            // float 64
            this.writeU8(0xcb);
            this.writeF64(object);
        }
    }
    encodeBigInt64(object) {
        if (object >= BigInt(0)) {
            // uint 64
            this.writeU8(0xcf);
            this.writeBigUint64(object);
        } else {
            // int 64
            this.writeU8(0xd3);
            this.writeBigInt64(object);
        }
    }
    writeStringHeader(byteLength) {
        if (byteLength < 32) {
            // fixstr
            this.writeU8(0xa0 + byteLength);
        } else if (byteLength < 0x100) {
            // str 8
            this.writeU8(0xd9);
            this.writeU8(byteLength);
        } else if (byteLength < 0x10000) {
            // str 16
            this.writeU8(0xda);
            this.writeU16(byteLength);
        } else if (byteLength < 0x100000000) {
            // str 32
            this.writeU8(0xdb);
            this.writeU32(byteLength);
        } else {
            throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);
        }
    }
    encodeString(object) {
        const maxHeaderSize = 1 + 4;
        const byteLength = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utf8Count"])(object);
        this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);
        this.writeStringHeader(byteLength);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utf8Encode"])(object, this.bytes, this.pos);
        this.pos += byteLength;
    }
    encodeObject(object, depth) {
        // try to encode objects with custom codec first of non-primitives
        const ext = this.extensionCodec.tryToEncode(object, this.context);
        if (ext != null) {
            this.encodeExtension(ext);
        } else if (Array.isArray(object)) {
            this.encodeArray(object, depth);
        } else if (ArrayBuffer.isView(object)) {
            this.encodeBinary(object);
        } else if (typeof object === "object") {
            this.encodeMap(object, depth);
        } else {
            // symbol, function and other special object come here unless extensionCodec handles them.
            throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);
        }
    }
    encodeBinary(object) {
        const size = object.byteLength;
        if (size < 0x100) {
            // bin 8
            this.writeU8(0xc4);
            this.writeU8(size);
        } else if (size < 0x10000) {
            // bin 16
            this.writeU8(0xc5);
            this.writeU16(size);
        } else if (size < 0x100000000) {
            // bin 32
            this.writeU8(0xc6);
            this.writeU32(size);
        } else {
            throw new Error(`Too large binary: ${size}`);
        }
        const bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$typedArrays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureUint8Array"])(object);
        this.writeU8a(bytes);
    }
    encodeArray(object, depth) {
        const size = object.length;
        if (size < 16) {
            // fixarray
            this.writeU8(0x90 + size);
        } else if (size < 0x10000) {
            // array 16
            this.writeU8(0xdc);
            this.writeU16(size);
        } else if (size < 0x100000000) {
            // array 32
            this.writeU8(0xdd);
            this.writeU32(size);
        } else {
            throw new Error(`Too large array: ${size}`);
        }
        for (const item of object){
            this.doEncode(item, depth + 1);
        }
    }
    countWithoutUndefined(object, keys) {
        let count = 0;
        for (const key of keys){
            if (object[key] !== undefined) {
                count++;
            }
        }
        return count;
    }
    encodeMap(object, depth) {
        const keys = Object.keys(object);
        if (this.sortKeys) {
            keys.sort();
        }
        const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;
        if (size < 16) {
            // fixmap
            this.writeU8(0x80 + size);
        } else if (size < 0x10000) {
            // map 16
            this.writeU8(0xde);
            this.writeU16(size);
        } else if (size < 0x100000000) {
            // map 32
            this.writeU8(0xdf);
            this.writeU32(size);
        } else {
            throw new Error(`Too large map object: ${size}`);
        }
        for (const key of keys){
            const value = object[key];
            if (!(this.ignoreUndefined && value === undefined)) {
                this.encodeString(key);
                this.doEncode(value, depth + 1);
            }
        }
    }
    encodeExtension(ext) {
        if (typeof ext.data === "function") {
            const data = ext.data(this.pos + 6);
            const size = data.length;
            if (size >= 0x100000000) {
                throw new Error(`Too large extension object: ${size}`);
            }
            this.writeU8(0xc9);
            this.writeU32(size);
            this.writeI8(ext.type);
            this.writeU8a(data);
            return;
        }
        const size = ext.data.length;
        if (size === 1) {
            // fixext 1
            this.writeU8(0xd4);
        } else if (size === 2) {
            // fixext 2
            this.writeU8(0xd5);
        } else if (size === 4) {
            // fixext 4
            this.writeU8(0xd6);
        } else if (size === 8) {
            // fixext 8
            this.writeU8(0xd7);
        } else if (size === 16) {
            // fixext 16
            this.writeU8(0xd8);
        } else if (size < 0x100) {
            // ext 8
            this.writeU8(0xc7);
            this.writeU8(size);
        } else if (size < 0x10000) {
            // ext 16
            this.writeU8(0xc8);
            this.writeU16(size);
        } else if (size < 0x100000000) {
            // ext 32
            this.writeU8(0xc9);
            this.writeU32(size);
        } else {
            throw new Error(`Too large extension object: ${size}`);
        }
        this.writeI8(ext.type);
        this.writeU8a(ext.data);
    }
    writeU8(value) {
        this.ensureBufferSizeToWrite(1);
        this.view.setUint8(this.pos, value);
        this.pos++;
    }
    writeU8a(values) {
        const size = values.length;
        this.ensureBufferSizeToWrite(size);
        this.bytes.set(values, this.pos);
        this.pos += size;
    }
    writeI8(value) {
        this.ensureBufferSizeToWrite(1);
        this.view.setInt8(this.pos, value);
        this.pos++;
    }
    writeU16(value) {
        this.ensureBufferSizeToWrite(2);
        this.view.setUint16(this.pos, value);
        this.pos += 2;
    }
    writeI16(value) {
        this.ensureBufferSizeToWrite(2);
        this.view.setInt16(this.pos, value);
        this.pos += 2;
    }
    writeU32(value) {
        this.ensureBufferSizeToWrite(4);
        this.view.setUint32(this.pos, value);
        this.pos += 4;
    }
    writeI32(value) {
        this.ensureBufferSizeToWrite(4);
        this.view.setInt32(this.pos, value);
        this.pos += 4;
    }
    writeF32(value) {
        this.ensureBufferSizeToWrite(4);
        this.view.setFloat32(this.pos, value);
        this.pos += 4;
    }
    writeF64(value) {
        this.ensureBufferSizeToWrite(8);
        this.view.setFloat64(this.pos, value);
        this.pos += 8;
    }
    writeU64(value) {
        this.ensureBufferSizeToWrite(8);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setUint64"])(this.view, this.pos, value);
        this.pos += 8;
    }
    writeI64(value) {
        this.ensureBufferSizeToWrite(8);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setInt64"])(this.view, this.pos, value);
        this.pos += 8;
    }
    writeBigUint64(value) {
        this.ensureBufferSizeToWrite(8);
        this.view.setBigUint64(this.pos, value);
        this.pos += 8;
    }
    writeBigInt64(value) {
        this.ensureBufferSizeToWrite(8);
        this.view.setBigInt64(this.pos, value);
        this.pos += 8;
    }
} //# sourceMappingURL=Encoder.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/encode.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "encode": (()=>encode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Encoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs [app-client] (ecmascript)");
;
function encode(value, options) {
    const encoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Encoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Encoder"](options);
    return encoder.encodeSharedRef(value);
} //# sourceMappingURL=encode.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prettyByte": (()=>prettyByte)
});
function prettyByte(byte) {
    return `${byte < 0 ? "-" : ""}0x${Math.abs(byte).toString(16).padStart(2, "0")}`;
} //# sourceMappingURL=prettyByte.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CachedKeyDecoder": (()=>CachedKeyDecoder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs [app-client] (ecmascript)");
;
const DEFAULT_MAX_KEY_LENGTH = 16;
const DEFAULT_MAX_LENGTH_PER_KEY = 16;
class CachedKeyDecoder {
    constructor(maxKeyLength = DEFAULT_MAX_KEY_LENGTH, maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY){
        this.hit = 0;
        this.miss = 0;
        this.maxKeyLength = maxKeyLength;
        this.maxLengthPerKey = maxLengthPerKey;
        // avoid `new Array(N)`, which makes a sparse array,
        // because a sparse array is typically slower than a non-sparse array.
        this.caches = [];
        for(let i = 0; i < this.maxKeyLength; i++){
            this.caches.push([]);
        }
    }
    canBeCached(byteLength) {
        return byteLength > 0 && byteLength <= this.maxKeyLength;
    }
    find(bytes, inputOffset, byteLength) {
        const records = this.caches[byteLength - 1];
        FIND_CHUNK: for (const record of records){
            const recordBytes = record.bytes;
            for(let j = 0; j < byteLength; j++){
                if (recordBytes[j] !== bytes[inputOffset + j]) {
                    continue FIND_CHUNK;
                }
            }
            return record.str;
        }
        return null;
    }
    store(bytes, value) {
        const records = this.caches[bytes.length - 1];
        const record = {
            bytes,
            str: value
        };
        if (records.length >= this.maxLengthPerKey) {
            // `records` are full!
            // Set `record` to an arbitrary position.
            records[Math.random() * records.length | 0] = record;
        } else {
            records.push(record);
        }
    }
    decode(bytes, inputOffset, byteLength) {
        const cachedValue = this.find(bytes, inputOffset, byteLength);
        if (cachedValue != null) {
            this.hit++;
            return cachedValue;
        }
        this.miss++;
        const str = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utf8DecodeJs"])(bytes, inputOffset, byteLength);
        // Ensure to copy a slice of bytes because the bytes may be a NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.
        const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);
        this.store(slicedCopyOfBytes, str);
        return str;
    }
} //# sourceMappingURL=CachedKeyDecoder.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Decoder": (()=>Decoder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$prettyByte$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtensionCodec$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$typedArrays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$CachedKeyDecoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
const STATE_ARRAY = "array";
const STATE_MAP_KEY = "map_key";
const STATE_MAP_VALUE = "map_value";
const mapKeyConverter = (key)=>{
    if (typeof key === "string" || typeof key === "number") {
        return key;
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"]("The type of key must be string or number but " + typeof key);
};
class StackPool {
    constructor(){
        this.stack = [];
        this.stackHeadPosition = -1;
    }
    get length() {
        return this.stackHeadPosition + 1;
    }
    top() {
        return this.stack[this.stackHeadPosition];
    }
    pushArrayState(size) {
        const state = this.getUninitializedStateFromPool();
        state.type = STATE_ARRAY;
        state.position = 0;
        state.size = size;
        state.array = new Array(size);
    }
    pushMapState(size) {
        const state = this.getUninitializedStateFromPool();
        state.type = STATE_MAP_KEY;
        state.readCount = 0;
        state.size = size;
        state.map = {};
    }
    getUninitializedStateFromPool() {
        this.stackHeadPosition++;
        if (this.stackHeadPosition === this.stack.length) {
            const partialState = {
                type: undefined,
                size: 0,
                array: undefined,
                position: 0,
                readCount: 0,
                map: undefined,
                key: null
            };
            this.stack.push(partialState);
        }
        return this.stack[this.stackHeadPosition];
    }
    release(state) {
        const topStackState = this.stack[this.stackHeadPosition];
        if (topStackState !== state) {
            throw new Error("Invalid stack state. Released state is not on top of the stack.");
        }
        if (state.type === STATE_ARRAY) {
            const partialState = state;
            partialState.size = 0;
            partialState.array = undefined;
            partialState.position = 0;
            partialState.type = undefined;
        }
        if (state.type === STATE_MAP_KEY || state.type === STATE_MAP_VALUE) {
            const partialState = state;
            partialState.size = 0;
            partialState.map = undefined;
            partialState.readCount = 0;
            partialState.type = undefined;
        }
        this.stackHeadPosition--;
    }
    reset() {
        this.stack.length = 0;
        this.stackHeadPosition = -1;
    }
}
const HEAD_BYTE_REQUIRED = -1;
const EMPTY_VIEW = new DataView(new ArrayBuffer(0));
const EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);
try {
    // IE11: The spec says it should throw RangeError,
    // IE11: but in IE11 it throws TypeError.
    EMPTY_VIEW.getInt8(0);
} catch (e) {
    if (!(e instanceof RangeError)) {
        throw new Error("This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access");
    }
}
const MORE_DATA = new RangeError("Insufficient data");
const sharedCachedKeyDecoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$CachedKeyDecoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CachedKeyDecoder"]();
class Decoder {
    constructor(options){
        this.totalPos = 0;
        this.pos = 0;
        this.view = EMPTY_VIEW;
        this.bytes = EMPTY_BYTES;
        this.headByte = HEAD_BYTE_REQUIRED;
        this.stack = new StackPool();
        this.entered = false;
        this.extensionCodec = options?.extensionCodec ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtensionCodec$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtensionCodec"].defaultCodec;
        this.context = options?.context; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined
        this.useBigInt64 = options?.useBigInt64 ?? false;
        this.rawStrings = options?.rawStrings ?? false;
        this.maxStrLength = options?.maxStrLength ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UINT32_MAX"];
        this.maxBinLength = options?.maxBinLength ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UINT32_MAX"];
        this.maxArrayLength = options?.maxArrayLength ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UINT32_MAX"];
        this.maxMapLength = options?.maxMapLength ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UINT32_MAX"];
        this.maxExtLength = options?.maxExtLength ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UINT32_MAX"];
        this.keyDecoder = options?.keyDecoder !== undefined ? options.keyDecoder : sharedCachedKeyDecoder;
        this.mapKeyConverter = options?.mapKeyConverter ?? mapKeyConverter;
    }
    clone() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        return new Decoder({
            extensionCodec: this.extensionCodec,
            context: this.context,
            useBigInt64: this.useBigInt64,
            rawStrings: this.rawStrings,
            maxStrLength: this.maxStrLength,
            maxBinLength: this.maxBinLength,
            maxArrayLength: this.maxArrayLength,
            maxMapLength: this.maxMapLength,
            maxExtLength: this.maxExtLength,
            keyDecoder: this.keyDecoder
        });
    }
    reinitializeState() {
        this.totalPos = 0;
        this.headByte = HEAD_BYTE_REQUIRED;
        this.stack.reset();
    // view, bytes, and pos will be re-initialized in setBuffer()
    }
    setBuffer(buffer) {
        const bytes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$typedArrays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureUint8Array"])(buffer);
        this.bytes = bytes;
        this.view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);
        this.pos = 0;
    }
    appendBuffer(buffer) {
        if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {
            this.setBuffer(buffer);
        } else {
            const remainingData = this.bytes.subarray(this.pos);
            const newData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$typedArrays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureUint8Array"])(buffer);
            // concat remainingData + newData
            const newBuffer = new Uint8Array(remainingData.length + newData.length);
            newBuffer.set(remainingData);
            newBuffer.set(newData, remainingData.length);
            this.setBuffer(newBuffer);
        }
    }
    hasRemaining(size) {
        return this.view.byteLength - this.pos >= size;
    }
    createExtraByteError(posToShow) {
        const { view, pos } = this;
        return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);
    }
    /**
     * @throws {@link DecodeError}
     * @throws {@link RangeError}
     */ decode(buffer) {
        if (this.entered) {
            const instance = this.clone();
            return instance.decode(buffer);
        }
        try {
            this.entered = true;
            this.reinitializeState();
            this.setBuffer(buffer);
            const object = this.doDecodeSync();
            if (this.hasRemaining(1)) {
                throw this.createExtraByteError(this.pos);
            }
            return object;
        } finally{
            this.entered = false;
        }
    }
    *decodeMulti(buffer) {
        if (this.entered) {
            const instance = this.clone();
            yield* instance.decodeMulti(buffer);
            return;
        }
        try {
            this.entered = true;
            this.reinitializeState();
            this.setBuffer(buffer);
            while(this.hasRemaining(1)){
                yield this.doDecodeSync();
            }
        } finally{
            this.entered = false;
        }
    }
    async decodeAsync(stream) {
        if (this.entered) {
            const instance = this.clone();
            return instance.decodeAsync(stream);
        }
        try {
            this.entered = true;
            let decoded = false;
            let object;
            for await (const buffer of stream){
                if (decoded) {
                    this.entered = false;
                    throw this.createExtraByteError(this.totalPos);
                }
                this.appendBuffer(buffer);
                try {
                    object = this.doDecodeSync();
                    decoded = true;
                } catch (e) {
                    if (!(e instanceof RangeError)) {
                        throw e; // rethrow
                    }
                // fallthrough
                }
                this.totalPos += this.pos;
            }
            if (decoded) {
                if (this.hasRemaining(1)) {
                    throw this.createExtraByteError(this.totalPos);
                }
                return object;
            }
            const { headByte, pos, totalPos } = this;
            throw new RangeError(`Insufficient data in parsing ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$prettyByte$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prettyByte"])(headByte)} at ${totalPos} (${pos} in the current buffer)`);
        } finally{
            this.entered = false;
        }
    }
    decodeArrayStream(stream) {
        return this.decodeMultiAsync(stream, true);
    }
    decodeStream(stream) {
        return this.decodeMultiAsync(stream, false);
    }
    async *decodeMultiAsync(stream, isArray) {
        if (this.entered) {
            const instance = this.clone();
            yield* instance.decodeMultiAsync(stream, isArray);
            return;
        }
        try {
            this.entered = true;
            let isArrayHeaderRequired = isArray;
            let arrayItemsLeft = -1;
            for await (const buffer of stream){
                if (isArray && arrayItemsLeft === 0) {
                    throw this.createExtraByteError(this.totalPos);
                }
                this.appendBuffer(buffer);
                if (isArrayHeaderRequired) {
                    arrayItemsLeft = this.readArraySize();
                    isArrayHeaderRequired = false;
                    this.complete();
                }
                try {
                    while(true){
                        yield this.doDecodeSync();
                        if (--arrayItemsLeft === 0) {
                            break;
                        }
                    }
                } catch (e) {
                    if (!(e instanceof RangeError)) {
                        throw e; // rethrow
                    }
                // fallthrough
                }
                this.totalPos += this.pos;
            }
        } finally{
            this.entered = false;
        }
    }
    doDecodeSync() {
        DECODE: while(true){
            const headByte = this.readHeadByte();
            let object;
            if (headByte >= 0xe0) {
                // negative fixint (111x xxxx) 0xe0 - 0xff
                object = headByte - 0x100;
            } else if (headByte < 0xc0) {
                if (headByte < 0x80) {
                    // positive fixint (0xxx xxxx) 0x00 - 0x7f
                    object = headByte;
                } else if (headByte < 0x90) {
                    // fixmap (1000 xxxx) 0x80 - 0x8f
                    const size = headByte - 0x80;
                    if (size !== 0) {
                        this.pushMapState(size);
                        this.complete();
                        continue DECODE;
                    } else {
                        object = {};
                    }
                } else if (headByte < 0xa0) {
                    // fixarray (1001 xxxx) 0x90 - 0x9f
                    const size = headByte - 0x90;
                    if (size !== 0) {
                        this.pushArrayState(size);
                        this.complete();
                        continue DECODE;
                    } else {
                        object = [];
                    }
                } else {
                    // fixstr (101x xxxx) 0xa0 - 0xbf
                    const byteLength = headByte - 0xa0;
                    object = this.decodeString(byteLength, 0);
                }
            } else if (headByte === 0xc0) {
                // nil
                object = null;
            } else if (headByte === 0xc2) {
                // false
                object = false;
            } else if (headByte === 0xc3) {
                // true
                object = true;
            } else if (headByte === 0xca) {
                // float 32
                object = this.readF32();
            } else if (headByte === 0xcb) {
                // float 64
                object = this.readF64();
            } else if (headByte === 0xcc) {
                // uint 8
                object = this.readU8();
            } else if (headByte === 0xcd) {
                // uint 16
                object = this.readU16();
            } else if (headByte === 0xce) {
                // uint 32
                object = this.readU32();
            } else if (headByte === 0xcf) {
                // uint 64
                if (this.useBigInt64) {
                    object = this.readU64AsBigInt();
                } else {
                    object = this.readU64();
                }
            } else if (headByte === 0xd0) {
                // int 8
                object = this.readI8();
            } else if (headByte === 0xd1) {
                // int 16
                object = this.readI16();
            } else if (headByte === 0xd2) {
                // int 32
                object = this.readI32();
            } else if (headByte === 0xd3) {
                // int 64
                if (this.useBigInt64) {
                    object = this.readI64AsBigInt();
                } else {
                    object = this.readI64();
                }
            } else if (headByte === 0xd9) {
                // str 8
                const byteLength = this.lookU8();
                object = this.decodeString(byteLength, 1);
            } else if (headByte === 0xda) {
                // str 16
                const byteLength = this.lookU16();
                object = this.decodeString(byteLength, 2);
            } else if (headByte === 0xdb) {
                // str 32
                const byteLength = this.lookU32();
                object = this.decodeString(byteLength, 4);
            } else if (headByte === 0xdc) {
                // array 16
                const size = this.readU16();
                if (size !== 0) {
                    this.pushArrayState(size);
                    this.complete();
                    continue DECODE;
                } else {
                    object = [];
                }
            } else if (headByte === 0xdd) {
                // array 32
                const size = this.readU32();
                if (size !== 0) {
                    this.pushArrayState(size);
                    this.complete();
                    continue DECODE;
                } else {
                    object = [];
                }
            } else if (headByte === 0xde) {
                // map 16
                const size = this.readU16();
                if (size !== 0) {
                    this.pushMapState(size);
                    this.complete();
                    continue DECODE;
                } else {
                    object = {};
                }
            } else if (headByte === 0xdf) {
                // map 32
                const size = this.readU32();
                if (size !== 0) {
                    this.pushMapState(size);
                    this.complete();
                    continue DECODE;
                } else {
                    object = {};
                }
            } else if (headByte === 0xc4) {
                // bin 8
                const size = this.lookU8();
                object = this.decodeBinary(size, 1);
            } else if (headByte === 0xc5) {
                // bin 16
                const size = this.lookU16();
                object = this.decodeBinary(size, 2);
            } else if (headByte === 0xc6) {
                // bin 32
                const size = this.lookU32();
                object = this.decodeBinary(size, 4);
            } else if (headByte === 0xd4) {
                // fixext 1
                object = this.decodeExtension(1, 0);
            } else if (headByte === 0xd5) {
                // fixext 2
                object = this.decodeExtension(2, 0);
            } else if (headByte === 0xd6) {
                // fixext 4
                object = this.decodeExtension(4, 0);
            } else if (headByte === 0xd7) {
                // fixext 8
                object = this.decodeExtension(8, 0);
            } else if (headByte === 0xd8) {
                // fixext 16
                object = this.decodeExtension(16, 0);
            } else if (headByte === 0xc7) {
                // ext 8
                const size = this.lookU8();
                object = this.decodeExtension(size, 1);
            } else if (headByte === 0xc8) {
                // ext 16
                const size = this.lookU16();
                object = this.decodeExtension(size, 2);
            } else if (headByte === 0xc9) {
                // ext 32
                const size = this.lookU32();
                object = this.decodeExtension(size, 4);
            } else {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Unrecognized type byte: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$prettyByte$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prettyByte"])(headByte)}`);
            }
            this.complete();
            const stack = this.stack;
            while(stack.length > 0){
                // arrays and maps
                const state = stack.top();
                if (state.type === STATE_ARRAY) {
                    state.array[state.position] = object;
                    state.position++;
                    if (state.position === state.size) {
                        object = state.array;
                        stack.release(state);
                    } else {
                        continue DECODE;
                    }
                } else if (state.type === STATE_MAP_KEY) {
                    if (object === "__proto__") {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"]("The key __proto__ is not allowed");
                    }
                    state.key = this.mapKeyConverter(object);
                    state.type = STATE_MAP_VALUE;
                    continue DECODE;
                } else {
                    // it must be `state.type === State.MAP_VALUE` here
                    state.map[state.key] = object;
                    state.readCount++;
                    if (state.readCount === state.size) {
                        object = state.map;
                        stack.release(state);
                    } else {
                        state.key = null;
                        state.type = STATE_MAP_KEY;
                        continue DECODE;
                    }
                }
            }
            return object;
        }
    }
    readHeadByte() {
        if (this.headByte === HEAD_BYTE_REQUIRED) {
            this.headByte = this.readU8();
        // console.log("headByte", prettyByte(this.headByte));
        }
        return this.headByte;
    }
    complete() {
        this.headByte = HEAD_BYTE_REQUIRED;
    }
    readArraySize() {
        const headByte = this.readHeadByte();
        switch(headByte){
            case 0xdc:
                return this.readU16();
            case 0xdd:
                return this.readU32();
            default:
                {
                    if (headByte < 0xa0) {
                        return headByte - 0x90;
                    } else {
                        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Unrecognized array type byte: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$prettyByte$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prettyByte"])(headByte)}`);
                    }
                }
        }
    }
    pushMapState(size) {
        if (size > this.maxMapLength) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);
        }
        this.stack.pushMapState(size);
    }
    pushArrayState(size) {
        if (size > this.maxArrayLength) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);
        }
        this.stack.pushArrayState(size);
    }
    decodeString(byteLength, headerOffset) {
        if (!this.rawStrings || this.stateIsMapKey()) {
            return this.decodeUtf8String(byteLength, headerOffset);
        }
        return this.decodeBinary(byteLength, headerOffset);
    }
    /**
     * @throws {@link RangeError}
     */ decodeUtf8String(byteLength, headerOffset) {
        if (byteLength > this.maxStrLength) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`);
        }
        if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {
            throw MORE_DATA;
        }
        const offset = this.pos + headerOffset;
        let object;
        if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {
            object = this.keyDecoder.decode(this.bytes, offset, byteLength);
        } else {
            object = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$utf8$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["utf8Decode"])(this.bytes, offset, byteLength);
        }
        this.pos += headerOffset + byteLength;
        return object;
    }
    stateIsMapKey() {
        if (this.stack.length > 0) {
            const state = this.stack.top();
            return state.type === STATE_MAP_KEY;
        }
        return false;
    }
    /**
     * @throws {@link RangeError}
     */ decodeBinary(byteLength, headOffset) {
        if (byteLength > this.maxBinLength) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);
        }
        if (!this.hasRemaining(byteLength + headOffset)) {
            throw MORE_DATA;
        }
        const offset = this.pos + headOffset;
        const object = this.bytes.subarray(offset, offset + byteLength);
        this.pos += headOffset + byteLength;
        return object;
    }
    decodeExtension(size, headOffset) {
        if (size > this.maxExtLength) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"](`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);
        }
        const extType = this.view.getInt8(this.pos + headOffset);
        const data = this.decodeBinary(size, headOffset + 1 /* extType */ );
        return this.extensionCodec.decode(data, extType, this.context);
    }
    lookU8() {
        return this.view.getUint8(this.pos);
    }
    lookU16() {
        return this.view.getUint16(this.pos);
    }
    lookU32() {
        return this.view.getUint32(this.pos);
    }
    readU8() {
        const value = this.view.getUint8(this.pos);
        this.pos++;
        return value;
    }
    readI8() {
        const value = this.view.getInt8(this.pos);
        this.pos++;
        return value;
    }
    readU16() {
        const value = this.view.getUint16(this.pos);
        this.pos += 2;
        return value;
    }
    readI16() {
        const value = this.view.getInt16(this.pos);
        this.pos += 2;
        return value;
    }
    readU32() {
        const value = this.view.getUint32(this.pos);
        this.pos += 4;
        return value;
    }
    readI32() {
        const value = this.view.getInt32(this.pos);
        this.pos += 4;
        return value;
    }
    readU64() {
        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUint64"])(this.view, this.pos);
        this.pos += 8;
        return value;
    }
    readI64() {
        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$int$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInt64"])(this.view, this.pos);
        this.pos += 8;
        return value;
    }
    readU64AsBigInt() {
        const value = this.view.getBigUint64(this.pos);
        this.pos += 8;
        return value;
    }
    readI64AsBigInt() {
        const value = this.view.getBigInt64(this.pos);
        this.pos += 8;
        return value;
    }
    readF32() {
        const value = this.view.getFloat32(this.pos);
        this.pos += 4;
        return value;
    }
    readF64() {
        const value = this.view.getFloat64(this.pos);
        this.pos += 8;
        return value;
    }
} //# sourceMappingURL=Decoder.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/decode.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "decode": (()=>decode),
    "decodeMulti": (()=>decodeMulti)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs [app-client] (ecmascript)");
;
function decode(buffer, options) {
    const decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoder"](options);
    return decoder.decode(buffer);
}
function decodeMulti(buffer, options) {
    const decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoder"](options);
    return decoder.decodeMulti(buffer);
} //# sourceMappingURL=decode.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/utils/stream.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// utility for whatwg streams
__turbopack_context__.s({
    "asyncIterableFromStream": (()=>asyncIterableFromStream),
    "ensureAsyncIterable": (()=>ensureAsyncIterable),
    "isAsyncIterable": (()=>isAsyncIterable)
});
function isAsyncIterable(object) {
    return object[Symbol.asyncIterator] != null;
}
async function* asyncIterableFromStream(stream) {
    const reader = stream.getReader();
    try {
        while(true){
            const { done, value } = await reader.read();
            if (done) {
                return;
            }
            yield value;
        }
    } finally{
        reader.releaseLock();
    }
}
function ensureAsyncIterable(streamLike) {
    if (isAsyncIterable(streamLike)) {
        return streamLike;
    } else {
        return asyncIterableFromStream(streamLike);
    }
} //# sourceMappingURL=stream.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/decodeAsync.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "decodeArrayStream": (()=>decodeArrayStream),
    "decodeAsync": (()=>decodeAsync),
    "decodeMultiStream": (()=>decodeMultiStream)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$stream$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/utils/stream.mjs [app-client] (ecmascript)");
;
;
async function decodeAsync(streamLike, options) {
    const stream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$stream$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureAsyncIterable"])(streamLike);
    const decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoder"](options);
    return decoder.decodeAsync(stream);
}
function decodeArrayStream(streamLike, options) {
    const stream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$stream$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureAsyncIterable"])(streamLike);
    const decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoder"](options);
    return decoder.decodeArrayStream(stream);
}
function decodeMultiStream(streamLike, options) {
    const stream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$utils$2f$stream$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureAsyncIterable"])(streamLike);
    const decoder = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoder"](options);
    return decoder.decodeStream(stream);
} //# sourceMappingURL=decodeAsync.mjs.map
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DecodeError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DecodeError"]),
    "Decoder": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Decoder"]),
    "EXT_TIMESTAMP": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXT_TIMESTAMP"]),
    "Encoder": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Encoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Encoder"]),
    "ExtData": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtData"]),
    "ExtensionCodec": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtensionCodec$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtensionCodec"]),
    "decode": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decode$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decode"]),
    "decodeArrayStream": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decodeAsync$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decodeArrayStream"]),
    "decodeAsync": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decodeAsync$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decodeAsync"]),
    "decodeMulti": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decode$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decodeMulti"]),
    "decodeMultiStream": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decodeAsync$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decodeMultiStream"]),
    "decodeTimestampExtension": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decodeTimestampExtension"]),
    "decodeTimestampToTimeSpec": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["decodeTimestampToTimeSpec"]),
    "encode": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$encode$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encode"]),
    "encodeDateToTimeSpec": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encodeDateToTimeSpec"]),
    "encodeTimeSpecToTimestamp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encodeTimeSpecToTimestamp"]),
    "encodeTimestampExtension": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["encodeTimestampExtension"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$encode$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/encode.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decode$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/decode.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$decodeAsync$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/decodeAsync.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Decoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$DecodeError$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$Encoder$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtensionCodec$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$ExtData$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$timestamp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <locals>");
}}),
"[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DecodeError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DecodeError"]),
    "Decoder": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Decoder"]),
    "EXT_TIMESTAMP": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["EXT_TIMESTAMP"]),
    "Encoder": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["Encoder"]),
    "ExtData": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ExtData"]),
    "ExtensionCodec": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ExtensionCodec"]),
    "decode": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decode"]),
    "decodeArrayStream": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decodeArrayStream"]),
    "decodeAsync": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decodeAsync"]),
    "decodeMulti": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decodeMulti"]),
    "decodeMultiStream": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decodeMultiStream"]),
    "decodeTimestampExtension": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decodeTimestampExtension"]),
    "decodeTimestampToTimeSpec": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["decodeTimestampToTimeSpec"]),
    "encode": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["encode"]),
    "encodeDateToTimeSpec": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["encodeDateToTimeSpec"]),
    "encodeTimeSpecToTimestamp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["encodeTimeSpecToTimestamp"]),
    "encodeTimestampExtension": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["encodeTimestampExtension"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$msgpack$2f$msgpack$2f$dist$2e$esm$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript) <exports>");
}}),
"[project]/node_modules/robot3/dist/machine.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use strict';
Object.defineProperty(exports, '__esModule', {
    value: true
});
function valueEnumerable(value) {
    return {
        enumerable: true,
        value
    };
}
function valueEnumerableWritable(value) {
    return {
        enumerable: true,
        writable: true,
        value
    };
}
let d = {};
let truthy = ()=>true;
let empty = ()=>({});
let identity = (a)=>a;
let callBoth = (par, fn, self, args)=>par.apply(self, args) && fn.apply(self, args);
let callForward = (par, fn, self, [a, b])=>fn.call(self, par.call(self, a, b), b);
let create = (a, b)=>Object.freeze(Object.create(a, b));
function stack(fns, def, caller) {
    return fns.reduce((par, fn)=>{
        return function(...args) {
            return caller(par, fn, this, args);
        };
    }, def);
}
function fnType(fn) {
    return create(this, {
        fn: valueEnumerable(fn)
    });
}
let reduceType = {};
let reduce = fnType.bind(reduceType);
let action = (fn)=>reduce((ctx, ev)=>!!~fn(ctx, ev) && ctx);
let guardType = {};
let guard = fnType.bind(guardType);
function filter(Type, arr) {
    return arr.filter((value)=>Type.isPrototypeOf(value));
}
function makeTransition(from, to, ...args) {
    let guards = stack(filter(guardType, args).map((t)=>t.fn), truthy, callBoth);
    let reducers = stack(filter(reduceType, args).map((t)=>t.fn), identity, callForward);
    return create(this, {
        from: valueEnumerable(from),
        to: valueEnumerable(to),
        guards: valueEnumerable(guards),
        reducers: valueEnumerable(reducers)
    });
}
let transitionType = {};
let immediateType = {};
let transition = makeTransition.bind(transitionType);
let immediate = makeTransition.bind(immediateType, null);
function enterImmediate(machine, service, event) {
    return transitionTo(service, machine, event, this.immediates) || machine;
}
function transitionsToMap(transitions) {
    let m = new Map();
    for (let t of transitions){
        if (!m.has(t.from)) m.set(t.from, []);
        m.get(t.from).push(t);
    }
    return m;
}
let stateType = {
    enter: identity
};
function state(...args) {
    let transitions = filter(transitionType, args);
    let immediates = filter(immediateType, args);
    let desc = {
        final: valueEnumerable(args.length === 0),
        transitions: valueEnumerable(transitionsToMap(transitions))
    };
    if (immediates.length) {
        desc.immediates = valueEnumerable(immediates);
        desc.enter = valueEnumerable(enterImmediate);
    }
    return create(stateType, desc);
}
let invokeFnType = {
    enter (machine2, service, event) {
        let rn = this.fn.call(service, service.context, event);
        if (machine.isPrototypeOf(rn)) return create(invokeMachineType, {
            machine: valueEnumerable(rn),
            transitions: valueEnumerable(this.transitions)
        }).enter(machine2, service, event);
        rn.then((data)=>service.send({
                type: 'done',
                data
            })).catch((error)=>service.send({
                type: 'error',
                error
            }));
        return machine2;
    }
};
let invokeMachineType = {
    enter (machine, service, event) {
        service.child = interpret(this.machine, (s)=>{
            service.onChange(s);
            if (service.child == s && s.machine.state.value.final) {
                delete service.child;
                service.send({
                    type: 'done',
                    data: s.context
                });
            }
        }, service.context, event);
        if (service.child.machine.state.value.final) {
            let data = service.child.context;
            delete service.child;
            return transitionTo(service, machine, {
                type: 'done',
                data
            }, this.transitions.get('done'));
        }
        return machine;
    }
};
function invoke(fn, ...transitions) {
    let t = valueEnumerable(transitionsToMap(transitions));
    return machine.isPrototypeOf(fn) ? create(invokeMachineType, {
        machine: valueEnumerable(fn),
        transitions: t
    }) : create(invokeFnType, {
        fn: valueEnumerable(fn),
        transitions: t
    });
}
let machine = {
    get state () {
        return {
            name: this.current,
            value: this.states[this.current]
        };
    }
};
function createMachine(current, states, contextFn = empty) {
    if (typeof current !== 'string') {
        contextFn = states || empty;
        states = current;
        current = Object.keys(states)[0];
    }
    if (d._create) d._create(current, states);
    return create(machine, {
        context: valueEnumerable(contextFn),
        current: valueEnumerable(current),
        states: valueEnumerable(states)
    });
}
function transitionTo(service, machine, fromEvent, candidates) {
    let { context } = service;
    for (let { to, guards, reducers } of candidates){
        if (guards(context, fromEvent)) {
            service.context = reducers.call(service, context, fromEvent);
            let original = machine.original || machine;
            let newMachine = create(original, {
                current: valueEnumerable(to),
                original: {
                    value: original
                }
            });
            if (d._onEnter) d._onEnter(machine, to, service.context, context, fromEvent);
            let state = newMachine.state.value;
            return state.enter(newMachine, service, fromEvent);
        }
    }
}
function send(service, event) {
    let eventName = event.type || event;
    let { machine } = service;
    let { value: state, name: currentStateName } = machine.state;
    if (state.transitions.has(eventName)) {
        return transitionTo(service, machine, event, state.transitions.get(eventName)) || machine;
    } else {
        if (d._send) d._send(eventName, currentStateName);
    }
    return machine;
}
let service = {
    send (event) {
        this.machine = send(this, event);
        // TODO detect change
        this.onChange(this);
    }
};
function interpret(machine, onChange, initialContext, event) {
    let s = Object.create(service, {
        machine: valueEnumerableWritable(machine),
        context: valueEnumerableWritable(machine.context(initialContext, event)),
        onChange: valueEnumerable(onChange)
    });
    s.send = s.send.bind(s);
    s.machine = s.machine.state.value.enter(s.machine, s, event);
    return s;
}
exports.action = action;
exports.createMachine = createMachine;
exports.d = d;
exports.guard = guard;
exports.immediate = immediate;
exports.interpret = interpret;
exports.invoke = invoke;
exports.reduce = reduce;
exports.state = state;
exports.transition = transition;
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/realtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.realtimeImpl = void 0;
/* eslint-disable @typescript-eslint/no-explicit-any */ const msgpack_1 = __turbopack_context__.r("[project]/node_modules/@msgpack/msgpack/dist.esm/index.mjs [app-client] (ecmascript)");
const robot3_1 = __turbopack_context__.r("[project]/node_modules/robot3/dist/machine.js [app-client] (ecmascript)");
const auth_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/auth.js [app-client] (ecmascript)");
const response_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/response.js [app-client] (ecmascript)");
const runtime_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/runtime.js [app-client] (ecmascript)");
const utils_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/utils.js [app-client] (ecmascript)");
const initialState = ()=>({
        enqueuedMessage: undefined
    });
function hasToken(context) {
    return context.token !== undefined;
}
function noToken(context) {
    return !hasToken(context);
}
function enqueueMessage(context, event) {
    return Object.assign(Object.assign({}, context), {
        enqueuedMessage: event.message
    });
}
function closeConnection(context) {
    if (context.websocket && context.websocket.readyState === WebSocket.OPEN) {
        context.websocket.close();
    }
    return Object.assign(Object.assign({}, context), {
        websocket: undefined
    });
}
function sendMessage(context, event) {
    if (context.websocket && context.websocket.readyState === WebSocket.OPEN) {
        if (event.message instanceof Uint8Array) {
            context.websocket.send(event.message);
        } else {
            context.websocket.send((0, msgpack_1.encode)(event.message));
        }
        return Object.assign(Object.assign({}, context), {
            enqueuedMessage: undefined
        });
    }
    return Object.assign(Object.assign({}, context), {
        enqueuedMessage: event.message
    });
}
function expireToken(context) {
    return Object.assign(Object.assign({}, context), {
        token: undefined
    });
}
function setToken(context, event) {
    return Object.assign(Object.assign({}, context), {
        token: event.token
    });
}
function connectionEstablished(context, event) {
    return Object.assign(Object.assign({}, context), {
        websocket: event.websocket
    });
}
// State machine
const connectionStateMachine = (0, robot3_1.createMachine)("idle", {
    idle: (0, robot3_1.state)((0, robot3_1.transition)("send", "connecting", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)("expireToken", "idle", (0, robot3_1.reduce)(expireToken)), (0, robot3_1.transition)("close", "idle", (0, robot3_1.reduce)(closeConnection))),
    connecting: (0, robot3_1.state)((0, robot3_1.transition)("connecting", "connecting"), (0, robot3_1.transition)("connected", "active", (0, robot3_1.reduce)(connectionEstablished)), (0, robot3_1.transition)("connectionClosed", "idle", (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.transition)("send", "connecting", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)("close", "idle", (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.immediate)("authRequired", (0, robot3_1.guard)(noToken))),
    authRequired: (0, robot3_1.state)((0, robot3_1.transition)("initiateAuth", "authInProgress"), (0, robot3_1.transition)("send", "authRequired", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)("close", "idle", (0, robot3_1.reduce)(closeConnection))),
    authInProgress: (0, robot3_1.state)((0, robot3_1.transition)("authenticated", "connecting", (0, robot3_1.reduce)(setToken)), (0, robot3_1.transition)("unauthorized", "idle", (0, robot3_1.reduce)(expireToken), (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.transition)("send", "authInProgress", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)("close", "idle", (0, robot3_1.reduce)(closeConnection))),
    active: (0, robot3_1.state)((0, robot3_1.transition)("send", "active", (0, robot3_1.reduce)(sendMessage)), (0, robot3_1.transition)("unauthorized", "idle", (0, robot3_1.reduce)(expireToken)), (0, robot3_1.transition)("connectionClosed", "idle", (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.transition)("close", "idle", (0, robot3_1.reduce)(closeConnection))),
    failed: (0, robot3_1.state)((0, robot3_1.transition)("send", "failed"), (0, robot3_1.transition)("close", "idle", (0, robot3_1.reduce)(closeConnection)))
}, initialState);
function buildRealtimeUrl(app, { token, maxBuffering }) {
    if (maxBuffering !== undefined && (maxBuffering < 1 || maxBuffering > 60)) {
        throw new Error("The `maxBuffering` must be between 1 and 60 (inclusive)");
    }
    const queryParams = new URLSearchParams({
        fal_jwt_token: token
    });
    if (maxBuffering !== undefined) {
        queryParams.set("max_buffering", maxBuffering.toFixed(0));
    }
    const appId = (0, utils_1.ensureAppIdFormat)(app);
    return `wss://fal.run/${appId}/realtime?${queryParams.toString()}`;
}
const DEFAULT_THROTTLE_INTERVAL = 128;
function isUnauthorizedError(message) {
    // TODO we need better protocol definition with error codes
    return message["status"] === "error" && message["error"] === "Unauthorized";
}
/**
 * See https://www.rfc-editor.org/rfc/rfc6455.html#section-7.4.1
 */ const WebSocketErrorCodes = {
    NORMAL_CLOSURE: 1000,
    GOING_AWAY: 1001
};
const connectionCache = new Map();
const connectionCallbacks = new Map();
function reuseInterpreter(key, throttleInterval, onChange) {
    if (!connectionCache.has(key)) {
        const machine = (0, robot3_1.interpret)(connectionStateMachine, onChange);
        connectionCache.set(key, Object.assign(Object.assign({}, machine), {
            throttledSend: throttleInterval > 0 ? (0, utils_1.throttle)(machine.send, throttleInterval, true) : machine.send
        }));
    }
    return connectionCache.get(key);
}
const noop = ()=>{
/* No-op */ };
/**
 * A no-op connection that does not send any message.
 * Useful on the frameworks that reuse code for both ssr and csr (e.g. Next)
 * so the call when doing ssr has no side-effects.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const NoOpConnection = {
    send: noop,
    close: noop
};
function isSuccessfulResult(data) {
    return data.status !== "error" && data.type !== "x-fal-message" && !isFalErrorResult(data);
}
function isFalErrorResult(data) {
    return data.type === "x-fal-error";
}
/**
 * The default implementation of the realtime client.
 */ exports.realtimeImpl = {
    connect (app, handler) {
        const { // if running on React in the server, set clientOnly to true by default
        clientOnly = (0, utils_1.isReact)() && !(0, runtime_1.isBrowser)(), connectionKey = crypto.randomUUID(), maxBuffering, throttleInterval = DEFAULT_THROTTLE_INTERVAL } = handler;
        if (clientOnly && !(0, runtime_1.isBrowser)()) {
            return NoOpConnection;
        }
        let previousState;
        // Although the state machine is cached so we don't open multiple connections,
        // we still need to update the callbacks so we can call the correct references
        // when the state machine is reused. This is needed because the callbacks
        // are passed as part of the handler object, which can be different across
        // different calls to `connect`.
        connectionCallbacks.set(connectionKey, {
            onError: handler.onError,
            onResult: handler.onResult
        });
        const getCallbacks = ()=>connectionCallbacks.get(connectionKey);
        const stateMachine = reuseInterpreter(connectionKey, throttleInterval, ({ context, machine, send })=>{
            const { enqueuedMessage, token } = context;
            if (machine.current === "active" && enqueuedMessage) {
                send({
                    type: "send",
                    message: enqueuedMessage
                });
            }
            if (machine.current === "authRequired" && token === undefined && previousState !== machine.current) {
                send({
                    type: "initiateAuth"
                });
                (0, auth_1.getTemporaryAuthToken)(app).then((token)=>{
                    send({
                        type: "authenticated",
                        token
                    });
                    const tokenExpirationTimeout = Math.round(auth_1.TOKEN_EXPIRATION_SECONDS * 0.9 * 1000);
                    setTimeout(()=>{
                        send({
                            type: "expireToken"
                        });
                    }, tokenExpirationTimeout);
                }).catch((error)=>{
                    send({
                        type: "unauthorized",
                        error
                    });
                });
            }
            if (machine.current === "connecting" && previousState !== machine.current && token !== undefined) {
                const ws = new WebSocket(buildRealtimeUrl(app, {
                    token,
                    maxBuffering
                }));
                ws.onopen = ()=>{
                    send({
                        type: "connected",
                        websocket: ws
                    });
                };
                ws.onclose = (event)=>{
                    if (event.code !== WebSocketErrorCodes.NORMAL_CLOSURE) {
                        const { onError = noop } = getCallbacks();
                        onError(new response_1.ApiError({
                            message: `Error closing the connection: ${event.reason}`,
                            status: event.code
                        }));
                    }
                    send({
                        type: "connectionClosed",
                        code: event.code
                    });
                };
                ws.onerror = (event)=>{
                    // TODO specify error protocol for identified errors
                    const { onError = noop } = getCallbacks();
                    onError(new response_1.ApiError({
                        message: "Unknown error",
                        status: 500
                    }));
                };
                ws.onmessage = (event)=>{
                    const { onResult } = getCallbacks();
                    // Handle binary messages as msgpack messages
                    if (event.data instanceof ArrayBuffer) {
                        const result = (0, msgpack_1.decode)(new Uint8Array(event.data));
                        onResult(result);
                        return;
                    }
                    if (event.data instanceof Uint8Array) {
                        const result = (0, msgpack_1.decode)(event.data);
                        onResult(result);
                        return;
                    }
                    if (event.data instanceof Blob) {
                        event.data.arrayBuffer().then((buffer)=>{
                            const result = (0, msgpack_1.decode)(new Uint8Array(buffer));
                            onResult(result);
                        });
                        return;
                    }
                    // Otherwise handle strings as plain JSON messages
                    const data = JSON.parse(event.data);
                    // Drop messages that are not related to the actual result.
                    // In the future, we might want to handle other types of messages.
                    // TODO: specify the fal ws protocol format
                    if (isUnauthorizedError(data)) {
                        send({
                            type: "unauthorized",
                            error: new Error("Unauthorized")
                        });
                        return;
                    }
                    if (isSuccessfulResult(data)) {
                        onResult(data);
                        return;
                    }
                    if (isFalErrorResult(data)) {
                        if (data.error === "TIMEOUT") {
                            // Timeout error messages just indicate that the connection hasn't
                            // received an incoming message for a while. We don't need to
                            // handle them as errors.
                            return;
                        }
                        const { onError = noop } = getCallbacks();
                        onError(new response_1.ApiError({
                            message: `${data.error}: ${data.reason}`,
                            // TODO better error status code
                            status: 400,
                            body: data
                        }));
                        return;
                    }
                };
            }
            previousState = machine.current;
        });
        const send = (input)=>{
            // Use throttled send to avoid sending too many messages
            var _a;
            const message = input instanceof Uint8Array ? input : Object.assign(Object.assign({}, input), {
                request_id: (_a = input["request_id"]) !== null && _a !== void 0 ? _a : crypto.randomUUID()
            });
            stateMachine.throttledSend({
                type: "send",
                message
            });
        };
        const close = ()=>{
            stateMachine.send({
                type: "close"
            });
        };
        return {
            send,
            close
        };
    }
}; //# sourceMappingURL=realtime.js.map
}}),
"[project]/node_modules/@fal-ai/serverless-client/src/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.parseAppId = exports.stream = exports.storage = exports.ValidationError = exports.ApiError = exports.realtime = exports.withProxy = exports.withMiddleware = exports.subscribe = exports.run = exports.queue = exports.getConfig = exports.config = void 0;
var config_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/config.js [app-client] (ecmascript)");
Object.defineProperty(exports, "config", {
    enumerable: true,
    get: function() {
        return config_1.config;
    }
});
Object.defineProperty(exports, "getConfig", {
    enumerable: true,
    get: function() {
        return config_1.getConfig;
    }
});
var function_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/function.js [app-client] (ecmascript)");
Object.defineProperty(exports, "queue", {
    enumerable: true,
    get: function() {
        return function_1.queue;
    }
});
Object.defineProperty(exports, "run", {
    enumerable: true,
    get: function() {
        return function_1.run;
    }
});
Object.defineProperty(exports, "subscribe", {
    enumerable: true,
    get: function() {
        return function_1.subscribe;
    }
});
var middleware_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/middleware.js [app-client] (ecmascript)");
Object.defineProperty(exports, "withMiddleware", {
    enumerable: true,
    get: function() {
        return middleware_1.withMiddleware;
    }
});
Object.defineProperty(exports, "withProxy", {
    enumerable: true,
    get: function() {
        return middleware_1.withProxy;
    }
});
var realtime_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/realtime.js [app-client] (ecmascript)");
Object.defineProperty(exports, "realtime", {
    enumerable: true,
    get: function() {
        return realtime_1.realtimeImpl;
    }
});
var response_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/response.js [app-client] (ecmascript)");
Object.defineProperty(exports, "ApiError", {
    enumerable: true,
    get: function() {
        return response_1.ApiError;
    }
});
Object.defineProperty(exports, "ValidationError", {
    enumerable: true,
    get: function() {
        return response_1.ValidationError;
    }
});
var storage_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/storage.js [app-client] (ecmascript)");
Object.defineProperty(exports, "storage", {
    enumerable: true,
    get: function() {
        return storage_1.storageImpl;
    }
});
var streaming_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/streaming.js [app-client] (ecmascript)");
Object.defineProperty(exports, "stream", {
    enumerable: true,
    get: function() {
        return streaming_1.stream;
    }
});
var utils_1 = __turbopack_context__.r("[project]/node_modules/@fal-ai/serverless-client/src/utils.js [app-client] (ecmascript)");
Object.defineProperty(exports, "parseAppId", {
    enumerable: true,
    get: function() {
        return utils_1.parseAppId;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Download)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M12 15V3",
            key: "m9g1x1"
        }
    ],
    [
        "path",
        {
            d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",
            key: "ih7n3h"
        }
    ],
    [
        "path",
        {
            d: "m7 10 5 5 5-5",
            key: "brsn70"
        }
    ]
];
const Download = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("download", __iconNode);
;
 //# sourceMappingURL=download.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Download": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Heart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",
            key: "c3ymky"
        }
    ]
];
const Heart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("heart", __iconNode);
;
 //# sourceMappingURL=heart.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript) <export default as Heart>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Heart": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Calendar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M8 2v4",
            key: "1cmpym"
        }
    ],
    [
        "path",
        {
            d: "M16 2v4",
            key: "4m81vk"
        }
    ],
    [
        "rect",
        {
            width: "18",
            height: "18",
            x: "3",
            y: "4",
            rx: "2",
            key: "1hopcy"
        }
    ],
    [
        "path",
        {
            d: "M3 10h18",
            key: "8toen8"
        }
    ]
];
const Calendar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("calendar", __iconNode);
;
 //# sourceMappingURL=calendar.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Calendar": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Search)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m21 21-4.34-4.34",
            key: "14j7rj"
        }
    ],
    [
        "circle",
        {
            cx: "11",
            cy: "11",
            r: "8",
            key: "4ej97u"
        }
    ]
];
const Search = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("search", __iconNode);
;
 //# sourceMappingURL=search.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Search": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Palette)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",
            key: "e79jfc"
        }
    ],
    [
        "circle",
        {
            cx: "13.5",
            cy: "6.5",
            r: ".5",
            fill: "currentColor",
            key: "1okk4w"
        }
    ],
    [
        "circle",
        {
            cx: "17.5",
            cy: "10.5",
            r: ".5",
            fill: "currentColor",
            key: "f64h9f"
        }
    ],
    [
        "circle",
        {
            cx: "6.5",
            cy: "12.5",
            r: ".5",
            fill: "currentColor",
            key: "qy21gx"
        }
    ],
    [
        "circle",
        {
            cx: "8.5",
            cy: "7.5",
            r: ".5",
            fill: "currentColor",
            key: "fotxhn"
        }
    ]
];
const Palette = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("palette", __iconNode);
;
 //# sourceMappingURL=palette.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-client] (ecmascript) <export default as Palette>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Palette": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$palette$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/palette.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=node_modules_042811af._.js.map