{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/lib/fal-client.ts"], "sourcesContent": ["import * as fal from \"@fal-ai/serverless-client\";\n\n// 配置fal.ai客户端\nfal.config({\n  credentials: process.env.FAL_KEY,\n});\n\nexport interface FalImageGenerationOptions {\n  prompt: string;\n  image_size?: \"square_hd\" | \"square\" | \"portrait_4_3\" | \"portrait_16_9\" | \"landscape_4_3\" | \"landscape_16_9\";\n  num_inference_steps?: number;\n  guidance_scale?: number;\n  num_images?: number;\n  enable_safety_checker?: boolean;\n}\n\nexport async function generateImage(options: FalImageGenerationOptions) {\n  try {\n    const result = await fal.subscribe(\"fal-ai/flux/schnell\", {\n      input: {\n        prompt: options.prompt,\n        image_size: options.image_size || \"landscape_4_3\",\n        num_inference_steps: options.num_inference_steps || 4,\n        num_images: options.num_images || 1,\n        enable_safety_checker: options.enable_safety_checker !== false,\n      },\n    });\n\n    return {\n      success: true,\n      data: result,\n    };\n  } catch (error) {\n    console.error(\"fal.ai生图失败:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"生图失败\",\n    };\n  }\n}\n\n// 360水冷屏幕优化的预设尺寸\nexport const SCREEN_PRESETS = {\n  \"360_square_480p\": { width: 480, height: 480, image_size: \"square\" as const },\n  \"360_square_640p\": { width: 640, height: 640, image_size: \"square_hd\" as const },\n  \"360_landscape\": { width: 640, height: 480, image_size: \"landscape_4_3\" as const },\n} as const;\n\nexport type ScreenPreset = keyof typeof SCREEN_PRESETS;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,cAAc;AACd,CAAA,GAAA,mKAAA,CAAA,SAAU,AAAD,EAAE;IACT,aAAa,QAAQ,GAAG,CAAC,OAAO;AAClC;AAWO,eAAe,cAAc,OAAkC;IACpE,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,YAAa,AAAD,EAAE,uBAAuB;YACxD,OAAO;gBACL,QAAQ,QAAQ,MAAM;gBACtB,YAAY,QAAQ,UAAU,IAAI;gBAClC,qBAAqB,QAAQ,mBAAmB,IAAI;gBACpD,YAAY,QAAQ,UAAU,IAAI;gBAClC,uBAAuB,QAAQ,qBAAqB,KAAK;YAC3D;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,mBAAmB;QAAE,OAAO;QAAK,QAAQ;QAAK,YAAY;IAAkB;IAC5E,mBAAmB;QAAE,OAAO;QAAK,QAAQ;QAAK,YAAY;IAAqB;IAC/E,iBAAiB;QAAE,OAAO;QAAK,QAAQ;QAAK,YAAY;IAAyB;AACnF", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/components/WallpaperGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Wand2, Loader2, Monitor } from 'lucide-react';\nimport { SCREEN_PRESETS, ScreenPreset } from '@/lib/fal-client';\n\nexport default function WallpaperGenerator() {\n  const [prompt, setPrompt] = useState('');\n  const [title, setTitle] = useState('');\n  const [preset, setPreset] = useState<ScreenPreset>('360_square_640p');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generatedImage, setGeneratedImage] = useState<string | null>(null);\n\n  const handleGenerate = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!prompt.trim()) {\n      alert('请输入生图提示词');\n      return;\n    }\n\n    setIsGenerating(true);\n    setGeneratedImage(null);\n\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          prompt: prompt.trim(),\n          title: title.trim(),\n          preset,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setGeneratedImage(result.wallpaper.imageUrl);\n        // 清空表单\n        setPrompt('');\n        setTitle('');\n      } else {\n        alert(`生成失败: ${result.error}`);\n      }\n    } catch (error) {\n      console.error('生成壁纸失败:', error);\n      alert('生成失败，请稍后重试');\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8\">\n      <div className=\"text-center mb-8\">\n        <h2 className=\"text-3xl font-bold text-gray-800 dark:text-white mb-2\">\n          AI壁纸生成器\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-300\">\n          输入描述，AI为您生成专属壁纸\n        </p>\n      </div>\n\n      <form onSubmit={handleGenerate} className=\"space-y-6\">\n        {/* 标题输入 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            壁纸标题 (可选)\n          </label>\n          <input\n            type=\"text\"\n            value={title}\n            onChange={(e) => setTitle(e.target.value)}\n            placeholder=\"为您的壁纸起个名字...\"\n            className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n        </div>\n\n        {/* 提示词输入 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            生图提示词 *\n          </label>\n          <textarea\n            value={prompt}\n            onChange={(e) => setPrompt(e.target.value)}\n            placeholder=\"描述您想要的壁纸，例如：美丽的山水风景，夕阳西下，湖水倒影...\"\n            rows={4}\n            className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none\"\n            required\n          />\n        </div>\n\n        {/* 屏幕预设选择 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            <Monitor className=\"inline h-4 w-4 mr-1\" />\n            360水冷屏幕预设\n          </label>\n          <select\n            value={preset}\n            onChange={(e) => setPreset(e.target.value as ScreenPreset)}\n            className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          >\n            <option value=\"360_square_480p\">方形 480×480 (标准)</option>\n            <option value=\"360_square_640p\">方形 640×640 (高清)</option>\n            <option value=\"360_landscape\">横屏 640×480 (宽屏)</option>\n          </select>\n          <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n            {SCREEN_PRESETS[preset].width} × {SCREEN_PRESETS[preset].height} 像素\n          </p>\n        </div>\n\n        {/* 生成按钮 */}\n        <button\n          type=\"submit\"\n          disabled={isGenerating || !prompt.trim()}\n          className=\"w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2\"\n        >\n          {isGenerating ? (\n            <>\n              <Loader2 className=\"h-5 w-5 animate-spin\" />\n              <span>生成中...</span>\n            </>\n          ) : (\n            <>\n              <Wand2 className=\"h-5 w-5\" />\n              <span>生成壁纸</span>\n            </>\n          )}\n        </button>\n      </form>\n\n      {/* 生成结果 */}\n      {generatedImage && (\n        <div className=\"mt-8 text-center\">\n          <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-4\">\n            生成成功！\n          </h3>\n          <div className=\"inline-block bg-gray-100 dark:bg-gray-700 p-4 rounded-lg\">\n            <img\n              src={generatedImage}\n              alt=\"生成的壁纸\"\n              className=\"max-w-full h-auto rounded-lg shadow-md\"\n              style={{ maxHeight: '300px' }}\n            />\n          </div>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n            壁纸已保存到图库，您可以在下方查看和下载\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,OAAO,IAAI;oBACnB,OAAO,MAAM,IAAI;oBACjB;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,kBAAkB,OAAO,SAAS,CAAC,QAAQ;gBAC3C,OAAO;gBACP,UAAU;gBACV,SAAS;YACX,OAAO;gBACL,MAAM,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwD;;;;;;kCAGtE,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;0BAKlD,8OAAC;gBAAK,UAAU;gBAAgB,WAAU;;kCAExC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAKd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,aAAY;gCACZ,MAAM;gCACN,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAKZ,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;kDACf,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAwB;;;;;;;0CAG7C,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,8OAAC;wCAAO,OAAM;kDAAkB;;;;;;kDAChC,8OAAC;wCAAO,OAAM;kDAAgB;;;;;;;;;;;;0CAEhC,8OAAC;gCAAE,WAAU;;oCACV,2HAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,KAAK;oCAAC;oCAAI,2HAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,MAAM;oCAAC;;;;;;;;;;;;;kCAKpE,8OAAC;wBACC,MAAK;wBACL,UAAU,gBAAgB,CAAC,OAAO,IAAI;wBACtC,WAAU;kCAET,6BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;yDAGR;;8CACE,8OAAC,+MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;YAOb,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAGzE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;4BACV,OAAO;gCAAE,WAAW;4BAAQ;;;;;;;;;;;kCAGhC,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;;;;;;;AAOvE", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/components/WallpaperGallery.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Download, Heart, Eye, Calendar } from 'lucide-react';\nimport { Wallpaper } from '@/types';\n\nexport default function WallpaperGallery() {\n  const [wallpapers, setWallpapers] = useState<Wallpaper[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState<'all' | 'popular'>('all');\n\n  useEffect(() => {\n    fetchWallpapers();\n  }, [filter]);\n\n  const fetchWallpapers = async () => {\n    try {\n      setLoading(true);\n      const url = filter === 'popular' ? '/api/wallpapers?popular=true' : '/api/wallpapers';\n      const response = await fetch(url);\n      const result = await response.json();\n      \n      if (result.success) {\n        setWallpapers(result.wallpapers);\n      }\n    } catch (error) {\n      console.error('获取壁纸失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDownload = async (wallpaper: Wallpaper) => {\n    try {\n      const response = await fetch(`/api/download/${wallpaper.id}`, {\n        method: 'POST',\n      });\n      const result = await response.json();\n      \n      if (result.success) {\n        // 创建下载链接\n        const link = document.createElement('a');\n        link.href = result.downloadUrl;\n        link.download = `${wallpaper.title}.${wallpaper.format}`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        \n        // 刷新数据以更新下载次数\n        fetchWallpapers();\n      } else {\n        alert('下载失败');\n      }\n    } catch (error) {\n      console.error('下载失败:', error);\n      alert('下载失败');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('zh-CN');\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* 过滤器 */}\n      <div className=\"flex justify-center mb-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm\">\n          <button\n            onClick={() => setFilter('all')}\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n              filter === 'all'\n                ? 'bg-purple-600 text-white'\n                : 'text-gray-600 dark:text-gray-300 hover:text-purple-600'\n            }`}\n          >\n            全部壁纸\n          </button>\n          <button\n            onClick={() => setFilter('popular')}\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n              filter === 'popular'\n                ? 'bg-purple-600 text-white'\n                : 'text-gray-600 dark:text-gray-300 hover:text-purple-600'\n            }`}\n          >\n            热门壁纸\n          </button>\n        </div>\n      </div>\n\n      {/* 壁纸网格 */}\n      {wallpapers.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <p className=\"text-gray-500 dark:text-gray-400 text-lg\">\n            暂无壁纸，快去生成第一张吧！\n          </p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {wallpapers.map((wallpaper) => (\n            <div\n              key={wallpaper.id}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\"\n            >\n              {/* 图片 */}\n              <div className=\"relative aspect-square overflow-hidden\">\n                <img\n                  src={wallpaper.thumbnailUrl}\n                  alt={wallpaper.title}\n                  className=\"w-full h-full object-cover hover:scale-105 transition-transform duration-300\"\n                />\n                {wallpaper.optimizedFor360 && (\n                  <div className=\"absolute top-2 right-2 bg-purple-600 text-white text-xs px-2 py-1 rounded-full\">\n                    360优化\n                  </div>\n                )}\n              </div>\n\n              {/* 信息 */}\n              <div className=\"p-4\">\n                <h3 className=\"font-semibold text-gray-800 dark:text-white mb-2 line-clamp-2\">\n                  {wallpaper.title}\n                </h3>\n                \n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\">\n                  {wallpaper.prompt}\n                </p>\n\n                {/* 标签 */}\n                <div className=\"flex flex-wrap gap-1 mb-3\">\n                  {wallpaper.tags.slice(0, 2).map((tag, index) => (\n                    <span\n                      key={index}\n                      className=\"text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full\"\n                    >\n                      {tag}\n                    </span>\n                  ))}\n                </div>\n\n                {/* 统计信息 */}\n                <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"flex items-center space-x-1\">\n                      <Download className=\"h-3 w-3\" />\n                      <span>{wallpaper.downloads}</span>\n                    </span>\n                    <span className=\"flex items-center space-x-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      <span>{formatDate(wallpaper.createdAt)}</span>\n                    </span>\n                  </div>\n                  <span className=\"text-xs\">\n                    {wallpaper.width}×{wallpaper.height}\n                  </span>\n                </div>\n\n                {/* 操作按钮 */}\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => handleDownload(wallpaper)}\n                    className=\"flex-1 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors flex items-center justify-center space-x-1\"\n                  >\n                    <Download className=\"h-4 w-4\" />\n                    <span>下载</span>\n                  </button>\n                  <button className=\"bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 p-2 rounded-lg transition-colors\">\n                    <Heart className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,MAAM,WAAW,YAAY,iCAAiC;YACpE,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,cAAc,OAAO,UAAU;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,EAAE;gBAC5D,QAAQ;YACV;YACA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS;gBACT,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG,OAAO,WAAW;gBAC9B,KAAK,QAAQ,GAAG,GAAG,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM,EAAE;gBACxD,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,cAAc;gBACd;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC;IACjD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,6BACA,0DACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,UAAU;4BACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,YACP,6BACA,0DACJ;sCACH;;;;;;;;;;;;;;;;;YAOJ,WAAW,MAAM,KAAK,kBACrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAA2C;;;;;;;;;;qCAK1D,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC;wBAEC,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK,UAAU,YAAY;wCAC3B,KAAK,UAAU,KAAK;wCACpB,WAAU;;;;;;oCAEX,UAAU,eAAe,kBACxB,8OAAC;wCAAI,WAAU;kDAAiF;;;;;;;;;;;;0CAOpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,UAAU,KAAK;;;;;;kDAGlB,8OAAC;wCAAE,WAAU;kDACV,UAAU,MAAM;;;;;;kDAInB,8OAAC;wCAAI,WAAU;kDACZ,UAAU,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACpC,8OAAC;gDAEC,WAAU;0DAET;+CAHI;;;;;;;;;;kDASX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,UAAU,SAAS;;;;;;;;;;;;kEAE5B,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,WAAW,UAAU,SAAS;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDAAK,WAAU;;oDACb,UAAU,KAAK;oDAAC;oDAAE,UAAU,MAAM;;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uBAlElB,UAAU,EAAE;;;;;;;;;;;;;;;;AA4E/B", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { Search, Palette, Download } from 'lucide-react';\nimport { useState } from 'react';\n\nexport default function Header() {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: 实现搜索功能\n    console.log('搜索:', searchQuery);\n  };\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <Palette className=\"h-8 w-8 text-purple-600\" />\n            <span className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n              AIICG\n            </span>\n          </div>\n\n          {/* Search Bar */}\n          <form onSubmit={handleSearch} className=\"flex-1 max-w-md mx-8\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"搜索壁纸...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n          </form>\n\n          {/* Navigation */}\n          <nav className=\"flex items-center space-x-4\">\n            <button className=\"flex items-center space-x-1 px-3 py-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors\">\n              <Download className=\"h-4 w-4\" />\n              <span className=\"hidden sm:inline\">下载</span>\n            </button>\n          </nav>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,eAAe;QACf,QAAQ,GAAG,CAAC,OAAO;IACrB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAAmD;;;;;;;;;;;;kCAMrE,8OAAC;wBAAK,UAAU;wBAAc,WAAU;kCACtC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}]}