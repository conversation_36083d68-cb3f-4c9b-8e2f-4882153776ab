{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/lib/data-store.ts"], "sourcesContent": ["import { Wallpaper } from '@/types';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nconst DATA_FILE = path.join(process.cwd(), 'data', 'wallpapers.json');\n\nexport class DataStore {\n  private async ensureDataDirectory() {\n    const dataDir = path.dirname(DATA_FILE);\n    try {\n      await fs.mkdir(dataDir, { recursive: true });\n    } catch (error) {\n      console.error('创建数据目录失败:', error);\n    }\n  }\n\n  async getAllWallpapers(): Promise<Wallpaper[]> {\n    try {\n      await this.ensureDataDirectory();\n      const data = await fs.readFile(DATA_FILE, 'utf-8');\n      return JSON.parse(data);\n    } catch (error) {\n      // 文件不存在时返回空数组\n      return [];\n    }\n  }\n\n  async saveWallpaper(wallpaper: Wallpaper): Promise<void> {\n    try {\n      const wallpapers = await this.getAllWallpapers();\n      wallpapers.unshift(wallpaper); // 新的壁纸放在最前面\n      \n      await this.ensureDataDirectory();\n      await fs.writeFile(DATA_FILE, JSON.stringify(wallpapers, null, 2));\n    } catch (error) {\n      console.error('保存壁纸数据失败:', error);\n      throw error;\n    }\n  }\n\n  async getWallpaperById(id: string): Promise<Wallpaper | null> {\n    const wallpapers = await this.getAllWallpapers();\n    return wallpapers.find(w => w.id === id) || null;\n  }\n\n  async updateWallpaperDownloads(id: string): Promise<void> {\n    try {\n      const wallpapers = await this.getAllWallpapers();\n      const wallpaper = wallpapers.find(w => w.id === id);\n      \n      if (wallpaper) {\n        wallpaper.downloads += 1;\n        await fs.writeFile(DATA_FILE, JSON.stringify(wallpapers, null, 2));\n      }\n    } catch (error) {\n      console.error('更新下载次数失败:', error);\n    }\n  }\n\n  async searchWallpapers(query: string): Promise<Wallpaper[]> {\n    const wallpapers = await this.getAllWallpapers();\n    const lowerQuery = query.toLowerCase();\n    \n    return wallpapers.filter(wallpaper => \n      wallpaper.title.toLowerCase().includes(lowerQuery) ||\n      wallpaper.prompt.toLowerCase().includes(lowerQuery) ||\n      wallpaper.tags.some(tag => tag.toLowerCase().includes(lowerQuery))\n    );\n  }\n\n  async getWallpapersByTag(tag: string): Promise<Wallpaper[]> {\n    const wallpapers = await this.getAllWallpapers();\n    return wallpapers.filter(wallpaper => \n      wallpaper.tags.includes(tag)\n    );\n  }\n\n  async getPopularWallpapers(limit: number = 10): Promise<Wallpaper[]> {\n    const wallpapers = await this.getAllWallpapers();\n    return wallpapers\n      .sort((a, b) => b.downloads - a.downloads)\n      .slice(0, limit);\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAE5C,MAAM;IACX,MAAc,sBAAsB;QAClC,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAC7B,IAAI;YACF,MAAM,qHAAA,CAAA,UAAE,CAAC,KAAK,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,mBAAyC;QAC7C,IAAI;YACF,MAAM,IAAI,CAAC,mBAAmB;YAC9B,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,WAAW;YAC1C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,cAAc;YACd,OAAO,EAAE;QACX;IACF;IAEA,MAAM,cAAc,SAAoB,EAAiB;QACvD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;YAC9C,WAAW,OAAO,CAAC,YAAY,YAAY;YAE3C,MAAM,IAAI,CAAC,mBAAmB;YAC9B,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,YAAY,MAAM;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,EAAU,EAA6B;QAC5D,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC9C;IAEA,MAAM,yBAAyB,EAAU,EAAiB;QACxD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;YAC9C,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEhD,IAAI,WAAW;gBACb,UAAU,SAAS,IAAI;gBACvB,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,YAAY,MAAM;YACjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,iBAAiB,KAAa,EAAwB;QAC1D,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,MAAM,aAAa,MAAM,WAAW;QAEpC,OAAO,WAAW,MAAM,CAAC,CAAA,YACvB,UAAU,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACvC,UAAU,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,UAAU,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;IAE1D;IAEA,MAAM,mBAAmB,GAAW,EAAwB;QAC1D,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,OAAO,WAAW,MAAM,CAAC,CAAA,YACvB,UAAU,IAAI,CAAC,QAAQ,CAAC;IAE5B;IAEA,MAAM,qBAAqB,QAAgB,EAAE,EAAwB;QACnE,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,OAAO,WACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/app/api/wallpapers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { DataStore } from '@/lib/data-store';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('q');\n    const tag = searchParams.get('tag');\n    const popular = searchParams.get('popular');\n\n    const dataStore = new DataStore();\n    let wallpapers;\n\n    if (query) {\n      wallpapers = await dataStore.searchWallpapers(query);\n    } else if (tag) {\n      wallpapers = await dataStore.getWallpapersByTag(tag);\n    } else if (popular) {\n      wallpapers = await dataStore.getPopularWallpapers(20);\n    } else {\n      wallpapers = await dataStore.getAllWallpapers();\n    }\n\n    return NextResponse.json({\n      success: true,\n      wallpapers,\n      total: wallpapers.length,\n    });\n\n  } catch (error) {\n    console.error('获取壁纸列表失败:', error);\n    return NextResponse.json(\n      { success: false, error: '获取壁纸列表失败' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,MAAM,aAAa,GAAG,CAAC;QAC7B,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,YAAY,IAAI,6HAAA,CAAA,YAAS;QAC/B,IAAI;QAEJ,IAAI,OAAO;YACT,aAAa,MAAM,UAAU,gBAAgB,CAAC;QAChD,OAAO,IAAI,KAAK;YACd,aAAa,MAAM,UAAU,kBAAkB,CAAC;QAClD,OAAO,IAAI,SAAS;YAClB,aAAa,MAAM,UAAU,oBAAoB,CAAC;QACpD,OAAO;YACL,aAAa,MAAM,UAAU,gBAAgB;QAC/C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,OAAO,WAAW,MAAM;QAC1B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}