module.exports = {

"[project]/.next-internal/server/app/api/generate/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/fal-client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SCREEN_PRESETS": (()=>SCREEN_PRESETS),
    "generateImage": (()=>generateImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fal$2d$ai$2f$serverless$2d$client$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@fal-ai/serverless-client/src/index.js [app-route] (ecmascript)");
;
// 配置fal.ai客户端
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fal$2d$ai$2f$serverless$2d$client$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["config"])({
    credentials: process.env.FAL_KEY
});
async function generateImage(options) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$fal$2d$ai$2f$serverless$2d$client$2f$src$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["subscribe"])("fal-ai/flux/schnell", {
            input: {
                prompt: options.prompt,
                image_size: options.image_size || "landscape_4_3",
                num_inference_steps: options.num_inference_steps || 4,
                num_images: options.num_images || 1,
                enable_safety_checker: options.enable_safety_checker !== false
            }
        });
        return {
            success: true,
            data: result
        };
    } catch (error) {
        console.error("fal.ai生图失败:", error);
        return {
            success: false,
            error: error instanceof Error ? error.message : "生图失败"
        };
    }
}
const SCREEN_PRESETS = {
    "360_square_480p": {
        width: 480,
        height: 480,
        image_size: "square"
    },
    "360_square_640p": {
        width: 640,
        height: 640,
        image_size: "square_hd"
    },
    "360_landscape": {
        width: 640,
        height: 480,
        image_size: "landscape_4_3"
    }
};
}}),
"[externals]/sharp [external] (sharp, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("sharp", () => require("sharp"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs/promises [external] (fs/promises, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[project]/src/lib/image-processor.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ImageProcessor": (()=>ImageProcessor)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/sharp [external] (sharp, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
;
;
;
class ImageProcessor {
    uploadDir;
    wallpaperDir;
    constructor(){
        this.uploadDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'uploads');
        this.wallpaperDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'public', 'wallpapers');
        this.ensureDirectories();
    }
    async ensureDirectories() {
        try {
            await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].mkdir(this.uploadDir, {
                recursive: true
            });
            await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].mkdir(this.wallpaperDir, {
                recursive: true
            });
        } catch (error) {
            console.error('创建目录失败:', error);
        }
    }
    async processImage(inputBuffer, filename, options) {
        const timestamp = Date.now();
        const baseName = `${timestamp}_${filename.replace(/\.[^/.]+$/, "")}`;
        // 原图处理
        const originalFilename = `${baseName}.${options.format}`;
        const originalPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.wallpaperDir, originalFilename);
        let sharpInstance = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(inputBuffer);
        // 360水冷屏幕优化
        if (options.optimize360) {
            sharpInstance = sharpInstance.resize(options.width, options.height, {
                fit: 'cover',
                position: 'center'
            }).sharpen() // 增强锐度，适合小屏幕显示
            .modulate({
                brightness: 1.1,
                saturation: 1.2,
                hue: 0
            });
        } else {
            sharpInstance = sharpInstance.resize(options.width, options.height, {
                fit: 'cover',
                position: 'center'
            });
        }
        // 根据格式设置质量
        switch(options.format){
            case 'jpg':
                sharpInstance = sharpInstance.jpeg({
                    quality: options.quality || 85,
                    progressive: true
                });
                break;
            case 'webp':
                sharpInstance = sharpInstance.webp({
                    quality: options.quality || 80,
                    effort: 6
                });
                break;
            case 'png':
                sharpInstance = sharpInstance.png({
                    compressionLevel: 6,
                    progressive: true
                });
                break;
        }
        await sharpInstance.toFile(originalPath);
        // 生成缩略图
        const thumbnailFilename = `${baseName}_thumb.webp`;
        const thumbnailPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(this.wallpaperDir, thumbnailFilename);
        await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(inputBuffer).resize(300, 200, {
            fit: 'cover',
            position: 'center'
        }).webp({
            quality: 70
        }).toFile(thumbnailPath);
        return {
            originalPath: `/wallpapers/${originalFilename}`,
            thumbnailPath: `/wallpapers/${thumbnailFilename}`
        };
    }
    async downloadAndProcess(imageUrl, filename, options) {
        try {
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`下载图片失败: ${response.statusText}`);
            }
            const buffer = Buffer.from(await response.arrayBuffer());
            return await this.processImage(buffer, filename, options);
        } catch (error) {
            console.error('下载并处理图片失败:', error);
            throw error;
        }
    }
}
}}),
"[project]/src/lib/data-store.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "DataStore": (()=>DataStore)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
const DATA_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data', 'wallpapers.json');
class DataStore {
    async ensureDataDirectory() {
        const dataDir = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].dirname(DATA_FILE);
        try {
            await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].mkdir(dataDir, {
                recursive: true
            });
        } catch (error) {
            console.error('创建数据目录失败:', error);
        }
    }
    async getAllWallpapers() {
        try {
            await this.ensureDataDirectory();
            const data = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].readFile(DATA_FILE, 'utf-8');
            return JSON.parse(data);
        } catch (error) {
            // 文件不存在时返回空数组
            return [];
        }
    }
    async saveWallpaper(wallpaper) {
        try {
            const wallpapers = await this.getAllWallpapers();
            wallpapers.unshift(wallpaper); // 新的壁纸放在最前面
            await this.ensureDataDirectory();
            await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].writeFile(DATA_FILE, JSON.stringify(wallpapers, null, 2));
        } catch (error) {
            console.error('保存壁纸数据失败:', error);
            throw error;
        }
    }
    async getWallpaperById(id) {
        const wallpapers = await this.getAllWallpapers();
        return wallpapers.find((w)=>w.id === id) || null;
    }
    async updateWallpaperDownloads(id) {
        try {
            const wallpapers = await this.getAllWallpapers();
            const wallpaper = wallpapers.find((w)=>w.id === id);
            if (wallpaper) {
                wallpaper.downloads += 1;
                await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].writeFile(DATA_FILE, JSON.stringify(wallpapers, null, 2));
            }
        } catch (error) {
            console.error('更新下载次数失败:', error);
        }
    }
    async searchWallpapers(query) {
        const wallpapers = await this.getAllWallpapers();
        const lowerQuery = query.toLowerCase();
        return wallpapers.filter((wallpaper)=>wallpaper.title.toLowerCase().includes(lowerQuery) || wallpaper.prompt.toLowerCase().includes(lowerQuery) || wallpaper.tags.some((tag)=>tag.toLowerCase().includes(lowerQuery)));
    }
    async getWallpapersByTag(tag) {
        const wallpapers = await this.getAllWallpapers();
        return wallpapers.filter((wallpaper)=>wallpaper.tags.includes(tag));
    }
    async getPopularWallpapers(limit = 10) {
        const wallpapers = await this.getAllWallpapers();
        return wallpapers.sort((a, b)=>b.downloads - a.downloads).slice(0, limit);
    }
}
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/app/api/generate/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fal$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/fal-client.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$image$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/image-processor.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/data-store.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/nanoid/index.js [app-route] (ecmascript) <locals>");
;
;
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { prompt, preset = '360_square_640p', title } = body;
        if (!prompt) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '请输入生图提示词'
            }, {
                status: 400
            });
        }
        // 获取预设配置
        const screenConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fal$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["SCREEN_PRESETS"][preset];
        if (!screenConfig) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: '无效的屏幕预设'
            }, {
                status: 400
            });
        }
        // 调用fal.ai生成图片
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$fal$2d$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateImage"])({
            prompt,
            image_size: screenConfig.image_size,
            num_inference_steps: 4,
            guidance_scale: 7.5,
            num_images: 1
        });
        if (!result.success || !result.data) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: result.error || '生图失败'
            }, {
                status: 500
            });
        }
        // 获取生成的图片URL
        const imageUrl = result.data.images[0].url;
        // 处理图片
        const imageProcessor = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$image$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ImageProcessor"]();
        const processedImages = await imageProcessor.downloadAndProcess(imageUrl, `generated_${Date.now()}`, {
            width: screenConfig.width,
            height: screenConfig.height,
            format: 'webp',
            quality: 85,
            optimize360: true
        });
        // 保存到数据库
        const wallpaper = {
            id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$nanoid$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["nanoid"])(),
            title: title || `AI生成壁纸 - ${prompt.slice(0, 20)}...`,
            prompt,
            imageUrl: processedImages.originalPath,
            thumbnailUrl: processedImages.thumbnailPath,
            width: screenConfig.width,
            height: screenConfig.height,
            format: 'webp',
            createdAt: new Date().toISOString(),
            downloads: 0,
            tags: [
                'AI生成',
                preset.replace('_', ' ')
            ],
            optimizedFor360: true
        };
        const dataStore = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$data$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["DataStore"]();
        await dataStore.saveWallpaper(wallpaper);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            wallpaper
        });
    } catch (error) {
        console.error('生成壁纸失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: '服务器内部错误'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__bd1c08a8._.js.map