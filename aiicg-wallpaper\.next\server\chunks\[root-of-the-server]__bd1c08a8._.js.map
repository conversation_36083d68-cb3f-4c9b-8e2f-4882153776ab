{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/lib/fal-client.ts"], "sourcesContent": ["import * as fal from \"@fal-ai/serverless-client\";\n\n// 配置fal.ai客户端\nfal.config({\n  credentials: process.env.FAL_KEY,\n});\n\nexport interface FalImageGenerationOptions {\n  prompt: string;\n  image_size?: \"square_hd\" | \"square\" | \"portrait_4_3\" | \"portrait_16_9\" | \"landscape_4_3\" | \"landscape_16_9\";\n  num_inference_steps?: number;\n  guidance_scale?: number;\n  num_images?: number;\n  enable_safety_checker?: boolean;\n}\n\nexport async function generateImage(options: FalImageGenerationOptions) {\n  try {\n    const result = await fal.subscribe(\"fal-ai/flux/schnell\", {\n      input: {\n        prompt: options.prompt,\n        image_size: options.image_size || \"landscape_4_3\",\n        num_inference_steps: options.num_inference_steps || 4,\n        num_images: options.num_images || 1,\n        enable_safety_checker: options.enable_safety_checker !== false,\n      },\n    });\n\n    return {\n      success: true,\n      data: result,\n    };\n  } catch (error) {\n    console.error(\"fal.ai生图失败:\", error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : \"生图失败\",\n    };\n  }\n}\n\n// 360水冷屏幕优化的预设尺寸\nexport const SCREEN_PRESETS = {\n  \"360_square_480p\": { width: 480, height: 480, image_size: \"square\" as const },\n  \"360_square_640p\": { width: 640, height: 640, image_size: \"square_hd\" as const },\n  \"360_landscape\": { width: 640, height: 480, image_size: \"landscape_4_3\" as const },\n} as const;\n\nexport type ScreenPreset = keyof typeof SCREEN_PRESETS;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,cAAc;AACd,CAAA,GAAA,qKAAA,CAAA,SAAU,AAAD,EAAE;IACT,aAAa,QAAQ,GAAG,CAAC,OAAO;AAClC;AAWO,eAAe,cAAc,OAAkC;IACpE,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,qKAAA,CAAA,YAAa,AAAD,EAAE,uBAAuB;YACxD,OAAO;gBACL,QAAQ,QAAQ,MAAM;gBACtB,YAAY,QAAQ,UAAU,IAAI;gBAClC,qBAAqB,QAAQ,mBAAmB,IAAI;gBACpD,YAAY,QAAQ,UAAU,IAAI;gBAClC,uBAAuB,QAAQ,qBAAqB,KAAK;YAC3D;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,mBAAmB;QAAE,OAAO;QAAK,QAAQ;QAAK,YAAY;IAAkB;IAC5E,mBAAmB;QAAE,OAAO;QAAK,QAAQ;QAAK,YAAY;IAAqB;IAC/E,iBAAiB;QAAE,OAAO;QAAK,QAAQ;QAAK,YAAY;IAAyB;AACnF", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/lib/image-processor.ts"], "sourcesContent": ["import sharp from 'sharp';\nimport { ImageProcessOptions } from '@/types';\nimport path from 'path';\nimport fs from 'fs/promises';\n\nexport class ImageProcessor {\n  private uploadDir: string;\n  private wallpaperDir: string;\n\n  constructor() {\n    this.uploadDir = path.join(process.cwd(), 'public', 'uploads');\n    this.wallpaperDir = path.join(process.cwd(), 'public', 'wallpapers');\n    this.ensureDirectories();\n  }\n\n  private async ensureDirectories() {\n    try {\n      await fs.mkdir(this.uploadDir, { recursive: true });\n      await fs.mkdir(this.wallpaperDir, { recursive: true });\n    } catch (error) {\n      console.error('创建目录失败:', error);\n    }\n  }\n\n  async processImage(\n    inputBuffer: Buffer,\n    filename: string,\n    options: ImageProcessOptions\n  ): Promise<{ originalPath: string; thumbnailPath: string }> {\n    const timestamp = Date.now();\n    const baseName = `${timestamp}_${filename.replace(/\\.[^/.]+$/, \"\")}`;\n    \n    // 原图处理\n    const originalFilename = `${baseName}.${options.format}`;\n    const originalPath = path.join(this.wallpaperDir, originalFilename);\n    \n    let sharpInstance = sharp(inputBuffer);\n    \n    // 360水冷屏幕优化\n    if (options.optimize360) {\n      sharpInstance = sharpInstance\n        .resize(options.width, options.height, {\n          fit: 'cover',\n          position: 'center'\n        })\n        .sharpen() // 增强锐度，适合小屏幕显示\n        .modulate({\n          brightness: 1.1, // 稍微增加亮度\n          saturation: 1.2, // 增加饱和度\n          hue: 0\n        });\n    } else {\n      sharpInstance = sharpInstance.resize(options.width, options.height, {\n        fit: 'cover',\n        position: 'center'\n      });\n    }\n\n    // 根据格式设置质量\n    switch (options.format) {\n      case 'jpg':\n        sharpInstance = sharpInstance.jpeg({ \n          quality: options.quality || 85,\n          progressive: true \n        });\n        break;\n      case 'webp':\n        sharpInstance = sharpInstance.webp({ \n          quality: options.quality || 80,\n          effort: 6 \n        });\n        break;\n      case 'png':\n        sharpInstance = sharpInstance.png({ \n          compressionLevel: 6,\n          progressive: true \n        });\n        break;\n    }\n\n    await sharpInstance.toFile(originalPath);\n\n    // 生成缩略图\n    const thumbnailFilename = `${baseName}_thumb.webp`;\n    const thumbnailPath = path.join(this.wallpaperDir, thumbnailFilename);\n    \n    await sharp(inputBuffer)\n      .resize(300, 200, { fit: 'cover', position: 'center' })\n      .webp({ quality: 70 })\n      .toFile(thumbnailPath);\n\n    return {\n      originalPath: `/wallpapers/${originalFilename}`,\n      thumbnailPath: `/wallpapers/${thumbnailFilename}`\n    };\n  }\n\n  async downloadAndProcess(\n    imageUrl: string,\n    filename: string,\n    options: ImageProcessOptions\n  ): Promise<{ originalPath: string; thumbnailPath: string }> {\n    try {\n      const response = await fetch(imageUrl);\n      if (!response.ok) {\n        throw new Error(`下载图片失败: ${response.statusText}`);\n      }\n      \n      const buffer = Buffer.from(await response.arrayBuffer());\n      return await this.processImage(buffer, filename, options);\n    } catch (error) {\n      console.error('下载并处理图片失败:', error);\n      throw error;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAEO,MAAM;IACH,UAAkB;IAClB,aAAqB;IAE7B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACpD,IAAI,CAAC,YAAY,GAAG,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU;QACvD,IAAI,CAAC,iBAAiB;IACxB;IAEA,MAAc,oBAAoB;QAChC,IAAI;YACF,MAAM,qHAAA,CAAA,UAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;gBAAE,WAAW;YAAK;YACjD,MAAM,qHAAA,CAAA,UAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE;gBAAE,WAAW;YAAK;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,aACJ,WAAmB,EACnB,QAAgB,EAChB,OAA4B,EAC8B;QAC1D,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,SAAS,OAAO,CAAC,aAAa,KAAK;QAEpE,OAAO;QACP,MAAM,mBAAmB,GAAG,SAAS,CAAC,EAAE,QAAQ,MAAM,EAAE;QACxD,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;QAElD,IAAI,gBAAgB,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE;QAE1B,YAAY;QACZ,IAAI,QAAQ,WAAW,EAAE;YACvB,gBAAgB,cACb,MAAM,CAAC,QAAQ,KAAK,EAAE,QAAQ,MAAM,EAAE;gBACrC,KAAK;gBACL,UAAU;YACZ,GACC,OAAO,GAAG,eAAe;aACzB,QAAQ,CAAC;gBACR,YAAY;gBACZ,YAAY;gBACZ,KAAK;YACP;QACJ,OAAO;YACL,gBAAgB,cAAc,MAAM,CAAC,QAAQ,KAAK,EAAE,QAAQ,MAAM,EAAE;gBAClE,KAAK;gBACL,UAAU;YACZ;QACF;QAEA,WAAW;QACX,OAAQ,QAAQ,MAAM;YACpB,KAAK;gBACH,gBAAgB,cAAc,IAAI,CAAC;oBACjC,SAAS,QAAQ,OAAO,IAAI;oBAC5B,aAAa;gBACf;gBACA;YACF,KAAK;gBACH,gBAAgB,cAAc,IAAI,CAAC;oBACjC,SAAS,QAAQ,OAAO,IAAI;oBAC5B,QAAQ;gBACV;gBACA;YACF,KAAK;gBACH,gBAAgB,cAAc,GAAG,CAAC;oBAChC,kBAAkB;oBAClB,aAAa;gBACf;gBACA;QACJ;QAEA,MAAM,cAAc,MAAM,CAAC;QAE3B,QAAQ;QACR,MAAM,oBAAoB,GAAG,SAAS,WAAW,CAAC;QAClD,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;QAEnD,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,aACT,MAAM,CAAC,KAAK,KAAK;YAAE,KAAK;YAAS,UAAU;QAAS,GACpD,IAAI,CAAC;YAAE,SAAS;QAAG,GACnB,MAAM,CAAC;QAEV,OAAO;YACL,cAAc,CAAC,YAAY,EAAE,kBAAkB;YAC/C,eAAe,CAAC,YAAY,EAAE,mBAAmB;QACnD;IACF;IAEA,MAAM,mBACJ,QAAgB,EAChB,QAAgB,EAChB,OAA4B,EAC8B;QAC1D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,UAAU,EAAE;YAClD;YAEA,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,SAAS,WAAW;YACrD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,UAAU;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;YAC5B,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/lib/data-store.ts"], "sourcesContent": ["import { Wallpaper } from '@/types';\nimport fs from 'fs/promises';\nimport path from 'path';\n\nconst DATA_FILE = path.join(process.cwd(), 'data', 'wallpapers.json');\n\nexport class DataStore {\n  private async ensureDataDirectory() {\n    const dataDir = path.dirname(DATA_FILE);\n    try {\n      await fs.mkdir(dataDir, { recursive: true });\n    } catch (error) {\n      console.error('创建数据目录失败:', error);\n    }\n  }\n\n  async getAllWallpapers(): Promise<Wallpaper[]> {\n    try {\n      await this.ensureDataDirectory();\n      const data = await fs.readFile(DATA_FILE, 'utf-8');\n      return JSON.parse(data);\n    } catch (error) {\n      // 文件不存在时返回空数组\n      return [];\n    }\n  }\n\n  async saveWallpaper(wallpaper: Wallpaper): Promise<void> {\n    try {\n      const wallpapers = await this.getAllWallpapers();\n      wallpapers.unshift(wallpaper); // 新的壁纸放在最前面\n      \n      await this.ensureDataDirectory();\n      await fs.writeFile(DATA_FILE, JSON.stringify(wallpapers, null, 2));\n    } catch (error) {\n      console.error('保存壁纸数据失败:', error);\n      throw error;\n    }\n  }\n\n  async getWallpaperById(id: string): Promise<Wallpaper | null> {\n    const wallpapers = await this.getAllWallpapers();\n    return wallpapers.find(w => w.id === id) || null;\n  }\n\n  async updateWallpaperDownloads(id: string): Promise<void> {\n    try {\n      const wallpapers = await this.getAllWallpapers();\n      const wallpaper = wallpapers.find(w => w.id === id);\n      \n      if (wallpaper) {\n        wallpaper.downloads += 1;\n        await fs.writeFile(DATA_FILE, JSON.stringify(wallpapers, null, 2));\n      }\n    } catch (error) {\n      console.error('更新下载次数失败:', error);\n    }\n  }\n\n  async searchWallpapers(query: string): Promise<Wallpaper[]> {\n    const wallpapers = await this.getAllWallpapers();\n    const lowerQuery = query.toLowerCase();\n    \n    return wallpapers.filter(wallpaper => \n      wallpaper.title.toLowerCase().includes(lowerQuery) ||\n      wallpaper.prompt.toLowerCase().includes(lowerQuery) ||\n      wallpaper.tags.some(tag => tag.toLowerCase().includes(lowerQuery))\n    );\n  }\n\n  async getWallpapersByTag(tag: string): Promise<Wallpaper[]> {\n    const wallpapers = await this.getAllWallpapers();\n    return wallpapers.filter(wallpaper => \n      wallpaper.tags.includes(tag)\n    );\n  }\n\n  async getPopularWallpapers(limit: number = 10): Promise<Wallpaper[]> {\n    const wallpapers = await this.getAllWallpapers();\n    return wallpapers\n      .sort((a, b) => b.downloads - a.downloads)\n      .slice(0, limit);\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;;;AAEA,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;AAE5C,MAAM;IACX,MAAc,sBAAsB;QAClC,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAC7B,IAAI;YACF,MAAM,qHAAA,CAAA,UAAE,CAAC,KAAK,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,mBAAyC;QAC7C,IAAI;YACF,MAAM,IAAI,CAAC,mBAAmB;YAC9B,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,WAAW;YAC1C,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,cAAc;YACd,OAAO,EAAE;QACX;IACF;IAEA,MAAM,cAAc,SAAoB,EAAiB;QACvD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;YAC9C,WAAW,OAAO,CAAC,YAAY,YAAY;YAE3C,MAAM,IAAI,CAAC,mBAAmB;YAC9B,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,YAAY,MAAM;QACjE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,MAAM,iBAAiB,EAAU,EAA6B;QAC5D,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO;IAC9C;IAEA,MAAM,yBAAyB,EAAU,EAAiB;QACxD,IAAI;YACF,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;YAC9C,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEhD,IAAI,WAAW;gBACb,UAAU,SAAS,IAAI;gBACvB,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,YAAY,MAAM;YACjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B;IACF;IAEA,MAAM,iBAAiB,KAAa,EAAwB;QAC1D,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,MAAM,aAAa,MAAM,WAAW;QAEpC,OAAO,WAAW,MAAM,CAAC,CAAA,YACvB,UAAU,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACvC,UAAU,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,eACxC,UAAU,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;IAE1D;IAEA,MAAM,mBAAmB,GAAW,EAAwB;QAC1D,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,OAAO,WAAW,MAAM,CAAC,CAAA,YACvB,UAAU,IAAI,CAAC,QAAQ,CAAC;IAE5B;IAEA,MAAM,qBAAqB,QAAgB,EAAE,EAAwB;QACnE,MAAM,aAAa,MAAM,IAAI,CAAC,gBAAgB;QAC9C,OAAO,WACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG;IACd;AACF", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/workSpace/AI/2API/aiicg-wallpaper/src/app/api/generate/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { generateImage, SCREEN_PRESETS, ScreenPreset } from '@/lib/fal-client';\nimport { ImageProcessor } from '@/lib/image-processor';\nimport { DataStore } from '@/lib/data-store';\nimport { Wallpaper } from '@/types';\nimport { nanoid } from 'nanoid';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { prompt, preset = '360_square_640p', title } = body;\n\n    if (!prompt) {\n      return NextResponse.json(\n        { success: false, error: '请输入生图提示词' },\n        { status: 400 }\n      );\n    }\n\n    // 获取预设配置\n    const screenConfig = SCREEN_PRESETS[preset as ScreenPreset];\n    if (!screenConfig) {\n      return NextResponse.json(\n        { success: false, error: '无效的屏幕预设' },\n        { status: 400 }\n      );\n    }\n\n    // 调用fal.ai生成图片\n    const result = await generateImage({\n      prompt,\n      image_size: screenConfig.image_size,\n      num_inference_steps: 4,\n      guidance_scale: 7.5,\n      num_images: 1,\n    });\n\n    if (!result.success || !result.data) {\n      return NextResponse.json(\n        { success: false, error: result.error || '生图失败' },\n        { status: 500 }\n      );\n    }\n\n    // 获取生成的图片URL\n    const imageUrl = result.data.images[0].url;\n    \n    // 处理图片\n    const imageProcessor = new ImageProcessor();\n    const processedImages = await imageProcessor.downloadAndProcess(\n      imageUrl,\n      `generated_${Date.now()}`,\n      {\n        width: screenConfig.width,\n        height: screenConfig.height,\n        format: 'webp',\n        quality: 85,\n        optimize360: true,\n      }\n    );\n\n    // 保存到数据库\n    const wallpaper: Wallpaper = {\n      id: nanoid(),\n      title: title || `AI生成壁纸 - ${prompt.slice(0, 20)}...`,\n      prompt,\n      imageUrl: processedImages.originalPath,\n      thumbnailUrl: processedImages.thumbnailPath,\n      width: screenConfig.width,\n      height: screenConfig.height,\n      format: 'webp',\n      createdAt: new Date().toISOString(),\n      downloads: 0,\n      tags: ['AI生成', preset.replace('_', ' ')],\n      optimizedFor360: true,\n    };\n\n    const dataStore = new DataStore();\n    await dataStore.saveWallpaper(wallpaper);\n\n    return NextResponse.json({\n      success: true,\n      wallpaper,\n    });\n\n  } catch (error) {\n    console.error('生成壁纸失败:', error);\n    return NextResponse.json(\n      { success: false, error: '服务器内部错误' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAEA;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,SAAS,iBAAiB,EAAE,KAAK,EAAE,GAAG;QAEtD,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAW,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,eAAe,6HAAA,CAAA,iBAAc,CAAC,OAAuB;QAC3D,IAAI,CAAC,cAAc;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,eAAe;QACf,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACjC;YACA,YAAY,aAAa,UAAU;YACnC,qBAAqB;YACrB,gBAAgB;YAChB,YAAY;QACd;QAEA,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,OAAO,KAAK,IAAI;YAAO,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,WAAW,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG;QAE1C,OAAO;QACP,MAAM,iBAAiB,IAAI,kIAAA,CAAA,iBAAc;QACzC,MAAM,kBAAkB,MAAM,eAAe,kBAAkB,CAC7D,UACA,CAAC,UAAU,EAAE,KAAK,GAAG,IAAI,EACzB;YACE,OAAO,aAAa,KAAK;YACzB,QAAQ,aAAa,MAAM;YAC3B,QAAQ;YACR,SAAS;YACT,aAAa;QACf;QAGF,SAAS;QACT,MAAM,YAAuB;YAC3B,IAAI,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD;YACT,OAAO,SAAS,CAAC,SAAS,EAAE,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC;YACpD;YACA,UAAU,gBAAgB,YAAY;YACtC,cAAc,gBAAgB,aAAa;YAC3C,OAAO,aAAa,KAAK;YACzB,QAAQ,aAAa,MAAM;YAC3B,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW;YACX,MAAM;gBAAC;gBAAQ,OAAO,OAAO,CAAC,KAAK;aAAK;YACxC,iBAAiB;QACnB;QAEA,MAAM,YAAY,IAAI,6HAAA,CAAA,YAAS;QAC/B,MAAM,UAAU,aAAa,CAAC;QAE9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAU,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}